export type RestaurantImage = {
  key: string;
  originUrl: string;
  remarks: string;
};

export type RestaurantAddress = {
  postalCode: string;
  prefectureId: number;
  prefectureName: string;
  cityId: number;
  cityName: string;
  wardId: number;
  wardName: string;
  buildingRoom: string;
};

export type RestaurantGenre = {
  genreId: number;
  name: string;
};

export type RestaurantTimeSlot = {
  from: string;
  to: string;
};

export type Restaurant = {
  sellingPointDescription: string | undefined;
  sellingPointImages: any;
  chefImages: any;
  dishImages: any;
  dishDescription: string | undefined;
  _id: string;
  name: string;
  nameEn: string;
  phoneNumber: string;
  address: RestaurantAddress;
  addressEn: string;
  accessInformation: string;
  accessInformationEn: string;
  genre: RestaurantGenre;
  images: RestaurantImage[];
  topImages?: RestaurantImage[];
  lunchTime: RestaurantTimeSlot | null;
  dinnerTime: RestaurantTimeSlot | null;
  lowestMenuPrice: number;
  highestMenuPrice: number;
  highestMenuPriceUSD: number;
  lowestMenuPriceUSD: number;
  // Additional fields for detail page
  description?: string;
  descriptionEn?: string;
  aboutDish?: string;
  aboutDishEn?: string;
  aboutChef?: string;
  aboutChefEn?: string;
  sellingPoint?: string;
  sellingPointEn?: string;
  smokingPolicy?: string;
  parkingInfo?: string;
  courses?: RestaurantCourse[];
};

export type RestaurantCourse = {
  id: string;
  name: string;
  nameEn?: string;
  description: string;
  descriptionEn?: string;
  price: number;
  priceUSD: number;
  duration: number; // in minutes
  timeSlot: 'lunch' | 'dinner' | 'both';
};

export type RestaurantApiResponse = {
  data: {
    docs: Restaurant[];
    totalDocs: number;
    limit: number;
    page: number;
    hasNextPage: boolean;
    hasPrevPage: boolean;
    totalPages: number;
    nextPage: number | null;
  };
};

export type RestaurantDetailApiResponse = {
  data: Restaurant;
};

export type RestaurantQueryParams = {
  page?: number;
  limit?: number;
  fromDate?: string;
  toDate?: string;
};

// Menu types
export type MenuType = 'INBOUND_TOURIST' | 'JAPANESE_PEOPLE';

export type RestaurantMenu = {
  _id: string;
  name: string;
  description: string;
  menuTimeZone: string;
  menuType: MenuType;
  duration: number;
  price: number;
  priceUSD: number;
  createdAt: Date;
  updatedAt: Date;
};

export type MenuApiResponse = {
  docs: RestaurantMenu[];
  totalDocs?: number;
  limit: number;
  page?: number;
  hasNextPage?: boolean;
  hasPrevPage?: boolean;
  totalPages?: number;
  nextPage?: number;
  prevPage?: number;
};

export type MenuQueryParams = {
  page?: number;
  limit?: number;
  menuType?: MenuType;
};
