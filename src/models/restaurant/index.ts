import type { MenuQueryParams, RestaurantQueryParams } from './types';

const restaurantQuery = {
  getRestaurants: (params?: RestaurantQueryParams) => ({
    apiUrl: '/customer/restaurants',
    customParams: params,
  }),
  getRestaurantDetail: (id: string) => ({
    apiUrl: `/customer/restaurants/${id}`,
  }),
  getRestaurantMenus: (id: string, params?: MenuQueryParams) => ({
    apiUrl: `/customer/restaurants/${id}/menus`,
    customParams: {
      page: 1,
      limit: 50,
      menuType: 'INBOUND_TOURIST',
      ...params,
    },
  }),
};

export default restaurantQuery;
