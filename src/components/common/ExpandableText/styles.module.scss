// ExpandableText Component Styles
// Professional styling for text expansion functionality matching Figma design

.spoilerControl {
  // Base styling for the expand/collapse control button
  color: #BC923A !important;
  font-size: 14px !important;
  font-weight: 400 !important;
  font-family: 'Roboto', sans-serif !important;
  line-height: 22px !important;
  text-align: right !important;
  width: 100% !important;
  display: block !important;
  margin-top: 8px !important;
  padding: 0 !important;
  border: none !important;
  background: none !important;
  cursor: pointer !important;
  transition: color 0.2s ease !important;

  // Hover state - matches Figma design specifications
  &:hover {
    color: #A8832F !important;
  }

  // Focus state for accessibility
  &:focus {
    outline: 2px solid #BC923A !important;
    outline-offset: 2px !important;
    color: #A8832F !important;
  }

  // Active state for better user feedback
  &:active {
    color: #9C7A30 !important;
  }

  // Ensure proper styling override for <PERSON><PERSON>'s default styles
  &.mantine-Spoiler-control {
    color: #BC923A !important;
    font-size: 14px !important;
    font-weight: 400 !important;
    font-family: 'Roboto', sans-serif !important;
    line-height: 22px !important;
    text-align: right !important;
    margin-top: 8px !important;
    padding: 0 !important;
    border: none !important;
    background: none !important;
    text-decoration: none !important;

    &:hover {
      color: #A8832F !important;
      text-decoration: none !important;
    }

    &:focus {
      outline: 2px solid #BC923A !important;
      outline-offset: 2px !important;
      color: #A8832F !important;
    }
  }
}

// Responsive adjustments for mobile devices
@media (max-width: 768px) {
  .spoilerControl {
    font-size: 13px !important;
    line-height: 20px !important;
    margin-top: 6px !important;

    &.mantine-Spoiler-control {
      font-size: 13px !important;
      line-height: 20px !important;
      margin-top: 6px !important;
    }
  }
}

// Additional utility classes for different use cases

// Left-aligned control variant
.spoilerControlLeft {
  composes: spoilerControl;
  text-align: left !important;

  &.mantine-Spoiler-control {
    text-align: left !important;
  }
}

// Center-aligned control variant
.spoilerControlCenter {
  composes: spoilerControl;
  text-align: center !important;

  &.mantine-Spoiler-control {
    text-align: center !important;
  }
}

// Compact variant with reduced spacing
.spoilerControlCompact {
  composes: spoilerControl;
  margin-top: 4px !important;
  font-size: 13px !important;
  line-height: 20px !important;

  &.mantine-Spoiler-control {
    margin-top: 4px !important;
    font-size: 13px !important;
    line-height: 20px !important;
  }
}

// Large variant for prominent text sections
.spoilerControlLarge {
  composes: spoilerControl;
  font-size: 16px !important;
  line-height: 24px !important;
  margin-top: 12px !important;

  &.mantine-Spoiler-control {
    font-size: 16px !important;
    line-height: 24px !important;
    margin-top: 12px !important;
  }
}

// High contrast variant for accessibility
.spoilerControlHighContrast {
  composes: spoilerControl;
  color: #000000 !important;

  &:hover {
    color: #333333 !important;
  }

  &:focus {
    outline: 3px solid #000000 !important;
    color: #333333 !important;
  }

  &.mantine-Spoiler-control {
    color: #000000 !important;

    &:hover {
      color: #333333 !important;
    }

    &:focus {
      outline: 3px solid #000000 !important;
      color: #333333 !important;
    }
  }
}

// Animation for smooth transitions
@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(-4px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

// Apply animation to the control when it appears
.spoilerControl {
  animation: fadeIn 0.2s ease-out;
}

// Ensure proper z-index for focus states
.spoilerControl:focus {
  position: relative;
  z-index: 1;
}
