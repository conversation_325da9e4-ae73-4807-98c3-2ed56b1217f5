'use client';

import { Spoiler, Text } from '@mantine/core';

import classes from './styles.module.scss';

export type ExpandableTextProps = {
  /** The text content to display */
  text?: string;
  /** Maximum number of lines to show when collapsed (default: 4) */
  maxLines?: number;
  /** Custom line height in pixels (default: 22) */
  lineHeight?: number;
  /** Text to show for expanding (default: "See more") */
  showLabel?: string;
  /** Text to show for collapsing (default: "See less") */
  hideLabel?: string;
  /** Custom font size in pixels (default: 14) */
  fontSize?: number;
  /** Custom font weight (default: 400) */
  fontWeight?: number;
  /** Custom font family (default: 'Roboto, sans-serif') */
  fontFamily?: string;
  /** Custom text color (default: '#111D2F') */
  textColor?: string;
  /** Custom control color (default: '#BC923A') */
  controlColor?: string;
  /** Custom control hover color (default: '#A8832F') */
  controlHoverColor?: string;
  /** Text alignment (default: 'justify') */
  textAlign?: 'left' | 'center' | 'right' | 'justify';
  /** Custom CSS class name for the container */
  className?: string;
  /** Custom CSS class name for the control button */
  controlClassName?: string;
  /** Whether to show the control on the right side (default: true) */
  rightAlignControl?: boolean;
  /** Callback when expand/collapse state changes */
  onToggle?: (expanded: boolean) => void;
  /** ARIA label for accessibility */
  ariaLabel?: string;
};

const ExpandableText = ({
  text,
  maxLines = 4,
  lineHeight = 22,
  showLabel = 'See more',
  hideLabel = 'See less',
  fontSize = 14,
  fontWeight = 400,
  fontFamily = 'Roboto, sans-serif',
  textColor = '#111D2F',
  controlColor = '#BC923A',
  controlHoverColor = '#A8832F',
  textAlign = 'justify',
  className,
  controlClassName,
  rightAlignControl = true,
  ariaLabel,
}: ExpandableTextProps) => {
  // Return null if no text is provided
  if (!text || text.trim().length === 0) {
    return null;
  }

  const maxHeight = maxLines * lineHeight;

  return (
    <div className={className} role="region" aria-label={ariaLabel || 'Expandable text content'}>
      <Spoiler
        maxHeight={maxHeight}
        showLabel={showLabel}
        hideLabel={hideLabel}
        classNames={{
          control: `${classes.spoilerControl} ${controlClassName || ''}`,
        }}
        styles={{
          control: {
            'color': controlColor,
            'fontSize': `${fontSize}px`,
            fontWeight,
            fontFamily,
            'lineHeight': `${lineHeight}px`,
            'textAlign': rightAlignControl ? 'right' : 'left',
            'width': '100%',
            'display': 'block',
            'marginTop': '8px',
            'padding': 0,
            'border': 'none',
            'background': 'none',
            'cursor': 'pointer',
            'transition': 'color 0.2s ease',
            '--hover-color': controlHoverColor,
          },
        }}
      >
        <Text
          style={{
            lineHeight: `${lineHeight}px`,
            fontSize: `${fontSize}px`,
            color: textColor,
            textAlign,
            wordBreak: 'break-word',
            fontFamily,
            fontWeight,
          }}
          component="div"
        >
          {text}
        </Text>
      </Spoiler>
    </div>
  );
};

export default ExpandableText;
