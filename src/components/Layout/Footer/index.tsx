import Logo from '@images/icon-logo.svg';
import { An<PERSON>, Divider, Flex, Group } from '@mantine/core';
import Link from 'next/link';
import React from 'react';

const Footer = () => {
  return (
    <Flex
      align="center"
      bg="gray.9"
      direction={{ base: 'column', sm: 'row' }}
      gap={{ base: 16, sm: 32 }}
      justify="center"
      py={{ base: 24, sm: 16 }}
    >
      <Link href="/">
        <Group align="center">
          <Logo height={32} width={144} />
        </Group>
      </Link>
      <Flex
        // direction={{ base: 'column', sm: 'row' }}
        gap={{ base: 8, sm: 24 }}
        wrap="wrap"
        justify="center"
      >
        <Flex gap={{ base: 8, sm: 24 }} justify="center">
          <Anchor
            c="white"
            component={Link}
            fz={{
              base: 12,
              sm: 14,
            }}
            href="/terms"
          >
            Term of Use
          </Anchor>
          <Divider
            h={12}
            orientation="vertical"
            c="gray.4"
            style={{
              alignSelf: 'center',
            }}
          />
          <Anchor
            c="white"
            component={Link}
            fz={{
              base: 12,
              sm: 14,
            }}
            href="/policy"
          >
            Privacy Policy
          </Anchor>
          <Divider
            h={12}
            orientation="vertical"
            c="gray.4"
            style={{
              alignSelf: 'center',
            }}
          />
        </Flex>
        <Flex gap={{ base: 8, sm: 24 }} justify="center">
          <Anchor
            c="white"
            component={Link}
            fz={{
              base: 12,
              sm: 14,
            }}
            href="/company-profile"
          >
            Company Profile
          </Anchor>
          <Divider
            h={12}
            orientation="vertical"
            c="gray.4"
            style={{
              alignSelf: 'center',
            }}
          />
          <Anchor
            c="white"
            component={Link}
            fz={{
              base: 12,
              sm: 14,
            }}
            href="/contact-us"
          >
            Contact Us
          </Anchor>
        </Flex>
      </Flex>
    </Flex>
  );
};

export default Footer;
