.container {
  min-height: 100vh;
  min-height: 100dvh;
  display: flex;
  flex-direction: column;
  position: relative;
}

.header {
  position: sticky;
  top: 0;
  z-index: 100;
  @media (max-width: 768px) {
    overflow-y: hidden;
  }
}

.main {
  flex-grow: 1;
  position: relative;
  width: 100%;
  display: flex;
  & > div {
    width: 100%;
  }
}

.isAuth {
  background: #F4F1ED;
  transition: 'background 0.3s';
}
