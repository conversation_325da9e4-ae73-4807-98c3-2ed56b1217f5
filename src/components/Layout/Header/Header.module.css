.root {
  background-color: var(--mantine-color-white);
  .lang-btn {
    background-color: var(--mantine-color-gray-1);
  }

  .alt-text {
    color: var(--mantine-color-gray-4);

    &.active {
      color: var(--mantine-color-black);
    }
  }
}

.authRoot {
  background-color: var(--mantine-color-primary-6);

  .lang-btn {
    background-color: rgba(0, 22, 57, 0.4);
  }

  .alt-text {
    color: var(--mantine-color-gray-5);

    &.active {
      color: var(--mantine-color-white);
    }
  }

  .user {
    color: var(--mantine-color-white);
  }
}

.desktopNav {
  display: none;
  @media (min-width: 48em) {
    display: flex;
  }
}

.desktopNavigation {
  display: none;
  @media (min-width: 48em) {
    display: flex;
  }
}

.navigation {
  display: flex;
  align-items: center;
  gap: 32px;
}

.navItem {
  transition: all 0.2s ease;
  
  &:hover {
    opacity: 0.8;
  }
  
  /* Merriweather font styles for navigation */
  font-family: 'Merriweather', serif !important;
}
