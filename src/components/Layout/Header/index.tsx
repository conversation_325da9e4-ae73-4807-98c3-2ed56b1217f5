'use client';
import HamburgerIcon from '@icons/icon-hamburger.svg';
import Logo from '@images/icon-logo.svg';
import { ActionIcon, Box, Button, Group } from '@mantine/core';
import LoginModal from 'components/LoginModal';
import { useUser } from 'hooks';

import { useDrawerStore } from 'hooks/zustand';
import dynamic from 'next/dynamic';
import Link from 'next/link';
import React, { useState } from 'react';
import classes from './Header.module.css';
import Navigation from './Navigation';

const UserMenu = dynamic(() => import('./UserMenu'));

type Props = {
  loggedIn?: boolean;
};

const Header = ({ loggedIn = false }: Props) => {
  const { setOpenDrawer } = useDrawerStore();
  const [loginModalOpened, setLoginModalOpened] = useState(false);
  useUser();

  const handleToggleLoginModal = (value: boolean) => setLoginModalOpened(value);
  return (
    <Group
      align="center"
      classNames={{ root: classes.root }}
      h="100%"
      justify="space-between"
      px={{ base: 16, sm: 24 }}
      py={16}
      wrap="nowrap"
    >
      {/* Left section: Logo */}
      <Box>
        <Link href="/">
          <Group align="center">
            <Logo width={137} height={32} />
          </Group>
        </Link>
      </Box>

      <Box className={classes.desktopNavigation} style={{ flexGrow: 1 }}>
        <Group justify="center">
          <Navigation />
        </Group>
      </Box>

      {/* Right section: Mobile hamburger and user menu */}
      <Group gap={16}>
        {/* Mobile hamburger menu */}
        <ActionIcon
          color={loggedIn ? 'white' : 'black'}
          hiddenFrom="sm"
          onClick={() => setOpenDrawer(true)}
          size={36}
          variant="transparent"
        >
          <HamburgerIcon />
        </ActionIcon>

        {/* User menu or auth buttons */}
        <Group className={classes.desktopNav} gap={16}>
          {loggedIn
            ? (
                <UserMenu loggedIn={loggedIn} />
              )
            : (
                <>
                  <Button
                    w={128}
                    variant="outline"
                    size="lg"
                    onClick={() => handleToggleLoginModal(true)}
                  >
                    Log in
                  </Button>
                  <Button
                    w={128}
                    component={Link}
                    href="/register"
                    size="lg"
                  >
                    Sign up
                  </Button>
                </>
              )}
        </Group>
      </Group>

      {loginModalOpened && (
        <LoginModal
          opened
          onClose={() => handleToggleLoginModal(false)}
        />
      )}
    </Group>
  );
};

export default Header;
