'use client';

import { Box } from '@mantine/core';
import { useUser } from 'hooks';
import { usePathname } from 'next/navigation';
import React from 'react';

import AppDrawer from './Drawer';
import Footer from './Footer';
import Header from './Header';
import HeaderDefault from './Header/HeaderDefault';
import styles from './styles.module.css';

type LayoutProps = {
  children: React.ReactNode;
  loggedIn?: boolean;
};

const Layout = ({ children, loggedIn = true }: LayoutProps) => {
  const pathname = usePathname();
  const { data: user } = useUser();

  // Use HeaderDefault for register page and customer verify account page, regular Header for all other pages
  const isRegisterPage = [
    '/register',
    '/customer-verify-account',
    '/forgot-password',
    '/customer-forgot-password',
  ].some(path => pathname.includes(path));

  return (
    <Box className={[styles.container, user ? styles.isAuth : ''].join(' ')}>
      <AppDrawer />
      <Box
        className={styles.header}
        component="header"
        h={{ base: 52, sm: 72 }}
      >
        {isRegisterPage
          ? (
              <HeaderDefault loggedIn={loggedIn} />
            )
          : (
              <Header loggedIn={loggedIn} />
            )}
      </Box>
      <Box
        className={styles.main}
        component="main"
      >
        <div>{children}</div>
      </Box>
      <Box component="footer">
        <Footer />
      </Box>
    </Box>
  );
};

export default Layout;
