'use client';

import IconCard from '@icons/icon-card.svg';
import IconClose from '@icons/icon-close.svg';
import IconLogout from '@icons/icon-logout.svg';
import IconUser from '@icons/icon-user.svg';
import Logo from '@images/icon-logo.svg';
import { Divider, Drawer, Flex, Group, NavLink, Stack } from '@mantine/core';
import LoginModal from 'components/LoginModal';
import Text from 'components/Typography';
import { useLogout, useUser } from 'hooks';
import { useDrawerStore } from 'hooks/zustand';
import Link from 'next/link';
import { usePathname } from 'next/navigation';
import React, { useState } from 'react';
import classes from './Drawer.module.scss';

const navigationItems = [
  { href: '/restaurant', label: 'Restaurant' },
  { href: '/about', label: 'About' },
  { href: '/faq', label: 'FAQ' },
  { href: '/contact', label: 'Contact Us' },
  { href: '/etiquette', label: 'Etiquette' },
];

const AppDrawer = () => {
  const [loginModalOpened, setLoginModalOpened] = useState(false);
  const { openedDrawer, setOpenDrawer } = useDrawerStore();
  const { data: user } = useUser();
  const { confirmLogout } = useLogout();
  const pathname = usePathname();

  const getFullname = () => {
    if (!user?.firstName && !user?.lastName) {
      return 'Unknown User';
    }

    return [user.firstName, user.lastName].join(' ');
  };

  const navLinkClassNames = {
    root: classes.navRoot,
    label: classes.label,
    icon: classes.icon,
  };
  const handleToggleLoginModal = (value: boolean) => setLoginModalOpened(value);

  return (
    <>
      <Drawer
        onClose={() => setOpenDrawer(false)}
        opened={openedDrawer}
        withCloseButton={false}
        withinPortal={false}
        position="right"
        styles={{
          header: {
            padding: '33px 0 10px 0',
          },
        }}

      >
        <Drawer.Header>
          <Drawer.Title w="100%">
            <Flex w="100%" align="center" justify="space-between">
              <Link href="/">
                <Group align="center">
                  <Logo width={144} height={32} />
                </Group>
              </Link>
              <IconClose
                style={{
                  width: 14,
                  height: 14,
                  marginRight: 12,
                }}
                color="#828282"
                onClick={() => setOpenDrawer(false)}
              />
            </Flex>
          </Drawer.Title>
          {/* <Drawer.CloseButton size={36} /> */}
        </Drawer.Header>
        <Stack gap={0} justify="flex-start" mah="100%" p={0}>
          {user
            ? (
                <>
                  <Divider />
                  <Group gap={0} p={0} mt={16}>
                    {/* <Avatar
                      alt={getFullname()}
                      radius="xl"
                      size={40}
                      src={getImageUrl(user.avatar)}
                    >
                      <DefaultAvatar />
                    </Avatar> */}
                    <Text fw={700} fz={14} lh="28px" style={{ fontFamily: 'Merriweather, serif' }}>
                      {getFullname()}
                    </Text>
                  </Group>

                  <NavLink
                    classNames={navLinkClassNames}
                    component={Link}
                    href="/my-page/profile"
                    label={(
                      <Flex align="center" pl={3}>
                        <IconUser
                          style={{
                            width: 18,
                            height: 18,
                          }}
                        />
                        <Text
                          fz={14}
                          ml={8}
                          fw={pathname === '/my-page/profile' ? 700 : 400}
                        >
                          My Profile
                        </Text>
                      </Flex>
                    )}
                    mt={8}
                    px={14}
                    onClick={() => setOpenDrawer(false)}
                  />
                  <NavLink
                    classNames={navLinkClassNames}
                    component={Link}
                    href="/my-page/cards"
                    label={(
                      <Flex align="center">
                        <IconCard
                          style={{
                            width: 24,
                            height: 24,
                          }}
                        />
                        <Text
                          fz={14}
                          ml={8}
                          fw={pathname === '/my-page/cards' ? 700 : 400}
                        >
                          Payment Methods
                        </Text>
                      </Flex>
                    )}
                    mt={8}
                    px={14}
                    onClick={() => setOpenDrawer(false)}
                  />

                  <Divider mt={10} />

                  {/* Navigation Items */}
                  <Flex direction="column" justify="space-between" style={{ height: 'calc(100vh - 283px)' }}>
                    <Flex direction="column" gap={4} align="center" mt={10}>
                      {navigationItems.map(({ href, label }) => (
                        <NavLink
                          key={href}
                          classNames={navLinkClassNames}
                          component={Link}
                          href={href}
                          label={(
                            <Text
                              fz={14}
                              fw={pathname === href ? 700 : 400}
                              style={{ fontFamily: 'Merriweather, serif' }}
                            >
                              {label}
                            </Text>
                          )}
                          onClick={() => setOpenDrawer(false)}
                        />
                      ))}
                    </Flex>

                    <NavLink
                      className="logout-btn"
                      classNames={navLinkClassNames}
                      label={(
                        <Flex align="center">
                          <IconLogout />
                          <Text fz={14} c="error.3" ml={8}>Logout</Text>
                        </Flex>
                      )}
                      onClick={() => {
                        setOpenDrawer(false);
                        confirmLogout();
                      }}
                    />
                  </Flex>
                </>
              )
            : (
                <>
                  <Divider />
                  <Text
                    classNames={navLinkClassNames}
                    onClick={() => {
                      setOpenDrawer(false);
                      setTimeout(() => handleToggleLoginModal(true), 50);
                    }}
                  >
                    Login
                  </Text>
                  <NavLink
                    classNames={navLinkClassNames}
                    component={Link}
                    href="/register"
                    label="Register"
                    onClick={() => setOpenDrawer(false)}
                    unstyled
                  />
                  <Divider mt={10} />

                  {/* Navigation Items */}
                  <Flex direction="column" justify="space-between" style={{ height: 'calc(100vh - 283px)' }}>
                    <Flex direction="column" gap={4} align="center" mt={10}>
                      {navigationItems.map(({ href, label }) => (
                        <NavLink
                          key={href}
                          classNames={navLinkClassNames}
                          component={Link}
                          href={href}
                          label={(
                            <Text
                              fz={14}
                              fw={pathname === href ? 700 : 400}
                              style={{ fontFamily: 'Merriweather, serif' }}
                            >
                              {label}
                            </Text>
                          )}
                          onClick={() => setOpenDrawer(false)}
                        />
                      ))}
                    </Flex>
                  </Flex>
                </>
              )}
        </Stack>
      </Drawer>
      {
        loginModalOpened && (
          <LoginModal
            opened
            onClose={() => handleToggleLoginModal(false)}
          />
        )
      }
    </>
  );
};

export default AppDrawer;
