'use client';

import { Box, Container, Group } from '@mantine/core';
import { useEffect, useState } from 'react';
import classes from './styles.module.scss';

type NavigationItem = {
  id: string;
  label: string;
  sectionId: string;
};

const navigationItems: NavigationItem[] = [
  { id: 'about-restaurant', label: 'About Restaurant', sectionId: 'about-restaurant' },
  { id: 'about-dish', label: 'About Dish', sectionId: 'about-dish' },
  { id: 'about-chef', label: 'About Chef', sectionId: 'about-chef' },
  { id: 'selling-point', label: 'Selling Point', sectionId: 'selling-point' },
  { id: 'course-section', label: 'Course', sectionId: 'course-section' },
  { id: 'restaurant-information', label: 'Restaurant Info', sectionId: 'restaurant-information' },
];

const StickyNavigation = () => {
  const [activeSection, setActiveSection] = useState('about-restaurant');
  const [isSticky, setIsSticky] = useState(false);

  const scrollToSection = (sectionId: string) => {
    const element = document.getElementById(sectionId);
    if (element) {
      const headerOffset = 120; // Account for sticky nav height + some padding
      const elementPosition = element.offsetTop;
      const offsetPosition = elementPosition - headerOffset;

      window.scrollTo({
        top: offsetPosition,
        behavior: 'smooth',
      });
    }
  };

  useEffect(() => {
    let ticking = false;

    const handleScroll = () => {
      if (!ticking) {
        requestAnimationFrame(() => {
          // Check if navigation should be sticky
          const heroSection = document.querySelector('.hero-section') as HTMLElement;
          if (heroSection) {
            const heroBottom = heroSection.getBoundingClientRect().bottom;
            const headerHeight = window.innerWidth >= 768 ? 72 : 52;
            setIsSticky(heroBottom <= headerHeight);
          }

          // Determine active section
          const sections = navigationItems.map(item => item.sectionId);
          const headerHeight = window.innerWidth >= 768 ? 72 : 52;
          const navigationHeight = 60; // Approximate navigation height
          const scrollOffset = headerHeight + navigationHeight + 20; // Extra offset for better UX
          const scrollPosition = window.scrollY + scrollOffset;

          for (let i = sections.length - 1; i >= 0; i--) {
            const sectionId = sections[i];
            if (!sectionId) {
              continue;
            }

            const section = document.getElementById(sectionId);
            if (section && section.offsetTop <= scrollPosition) {
              setActiveSection(sectionId);
              break;
            }
          }

          ticking = false;
        });
        ticking = true;
      }
    };

    window.addEventListener('scroll', handleScroll, { passive: true });
    handleScroll(); // Check initial state

    return () => window.removeEventListener('scroll', handleScroll);
  }, []);

  return (
    <Box
      className={`${classes.stickyNavigation} ${isSticky ? classes.sticky : ''}`}
      component="nav"
      role="navigation"
      aria-label="Page sections navigation"
    >
      <Container size="lg">
        <Group justify="center" gap={0} className={classes.navigationGroup}>
          {navigationItems.map(item => (
            <button
              key={item.id}
              className={`${classes.navItem} ${activeSection === item.sectionId ? classes.active : ''}`}
              onClick={() => scrollToSection(item.sectionId)}
              aria-label={`Navigate to ${item.label} section`}
              type="button"
            >
              {item.label}
            </button>
          ))}
        </Group>
      </Container>
    </Box>
  );
};

export default StickyNavigation;
