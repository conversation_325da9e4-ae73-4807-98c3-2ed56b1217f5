.stickyNavigation {
  background: white;
  border-bottom: 1px solid #E5E7EB;
  transition: all 0s ease !important;
  z-index: 100;

  // Hide on desktop (1025px+) where left sidebar is shown
  @media (min-width: 1025px) {
    display: none;
  }

  &.sticky {
    position: fixed;
    top: 72px; // Below main header
    left: 0;
    right: 0;
    box-shadow: 0 4px 12px 0 rgba(0, 0, 0, 0.1), 0 2px 4px 0 rgba(0, 0, 0, 0.06);
    animation: slideDown 0s ease !important;
    border-bottom: 1px solid #D1D5DB;
  }
}

@keyframes slideDown {
  from {
    transform: translateY(-100%);
    opacity: 0;
  }
  to {
    transform: translateY(0);
    opacity: 1;
  }
}

.navigationGroup {
  overflow-x: auto;
  overflow-y: hidden;
  scrollbar-width: none;
  -ms-overflow-style: none;
  white-space: nowrap;
  display: flex;
  flex-wrap: nowrap;
  
  &::-webkit-scrollbar {
    display: none;
  }
  
  // Add subtle scroll indicators on mobile
  @media (max-width: 768px) {
    &::before,
    &::after {
      content: '';
      position: sticky;
      width: 20px;
      height: 100%;
      pointer-events: none;
      z-index: 1;
    }
    
    &::before {
      left: 0;
      background: linear-gradient(to right, white, transparent);
    }
    
    &::after {
      right: 0;
      background: linear-gradient(to left, white, transparent);
    }
  }
}

.navItem {
  background: none;
  border: none;
  padding: 16px 24px;
  font-family: 'Roboto', sans-serif;
  font-size: 14px;
  font-weight: 400;
  color: #6B7280;
  cursor: pointer;
  transition: all 0.2s ease;
  white-space: nowrap;
  position: relative;
  border-bottom: 2px solid transparent;
  flex-shrink: 0; // Prevent items from shrinking
  
  &:hover {
    color: #BC923A;
    background-color: #FEF3E2;
  }
  
  &.active {
    color: #BC923A;
    font-weight: 500;
    border-bottom-color: #BC923A;
  }
  
  &:focus-visible {
    outline: 2px solid #BC923A;
    outline-offset: -2px;
  }
}

// Mobile responsive design
@media (max-width: 768px) {
  .stickyNavigation {
    &.sticky {
      top: 52px; // Mobile header height
    }
  }
  
  .navItem {
    padding: 12px 16px;
    font-size: 12px;
    min-width: 100px;
    text-align: center;
  }
  
  .navigationGroup {
    justify-content: flex-start;
    padding: 0 16px;
  }
}

// Desktop design adjustments
@media (min-width: 769px) {
  .navigationGroup {
    justify-content: center;
  }
  
  .navItem {
    min-width: 140px;
    text-align: center;
  }
} 