'use client';

import type { DatesRangeValue } from '@mantine/dates';
import SearchIcon from '@icons/icon-search.svg';
import { Button, Text, useMatches } from '@mantine/core';
import { RangeDateInput } from 'components/Form';
import { useState } from 'react';

import classes from './styles.module.scss';

export type SearchFilters = {
  searchQuery: string;
  startDate: Date | null;
  endDate: Date | null;
};

type SearchSectionProps = {
  onSearch: (filters: SearchFilters) => void;
  isLoading?: boolean;
};

const SearchSection = ({ onSearch, isLoading }: SearchSectionProps) => {
  const [dateRange, setDateRange] = useState<DatesRangeValue>([null, null]);

  const isMobile = useMatches({
    base: true,
    sm: false,
  });

  const handleSearch = () => {
    onSearch({
      searchQuery: '',
      startDate: dateRange[0],
      endDate: dateRange[1],
    });
  };

  return (
    <div className={classes.container}>
      <div className={isMobile ? classes.mobileLayout : classes.desktopLayout}>
        <div className={classes.fieldGroup}>
          <Text size="sm" c="gray.7" fw={500}>
            Reservation Date
          </Text>
          <RangeDateInput
            value={dateRange}
            onChange={setDateRange}
            size="lg"
            placeholder={['Select Date', 'Select Date']}
          />
        </div>

        <div className={classes.actionGroup}>
          <Button
            className={classes.searchButton}
            loading={isLoading}
            size="lg"
            fullWidth={isMobile}
            leftSection={<SearchIcon width={11} height={11} />}
            onClick={handleSearch}
          >
            Search
          </Button>
        </div>
      </div>
    </div>
  );
};

export default SearchSection;
