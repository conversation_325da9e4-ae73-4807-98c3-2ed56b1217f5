'use client';

import type { DatesRangeValue } from '@mantine/dates';
import SearchIcon from '@icons/icon-search.svg';
import { Button, Text, useMatches } from '@mantine/core';
import { RangeDateInput } from 'components/Form';
import { useState } from 'react';

import classes from './styles.module.scss';

export type SearchFilters = {
  searchQuery: string;
  startDate: Date | null;
  endDate: Date | null;
};

type SearchSectionProps = {
  onSearch: (filters: SearchFilters) => void;
  isLoading?: boolean; // Kept for backward compatibility but not used internally
};

const SearchSection = ({ onSearch, isLoading: _ }: SearchSectionProps) => {
  const today = new Date();
  const [dateRange, setDateRange] = useState<DatesRangeValue>([today, today]);
  const [isSearching, setIsSearching] = useState(false);

  const isMobile = useMatches({
    base: true,
    sm: false,
  });

  // Remove auto-trigger on date change - only search when button is clicked

  const handleSearch = () => {
    setIsSearching(true);

    onSearch({
      searchQuery: '',
      startDate: dateRange[0],
      endDate: dateRange[1],
    });

    setTimeout(() => {
      setIsSearching(false);
    }, 500);
  };

  return (
    <div className={classes.container}>
      <div className={isMobile ? classes.mobileLayout : classes.desktopLayout}>
        <div className={classes.fieldGroup}>
          <Text size="sm" c="gray.7" fw={500} ta={{ base: 'center', sm: 'left' }}>
            Reservation Date
          </Text>
          <RangeDateInput
            value={dateRange}
            onChange={setDateRange}
            size="lg"
            clearable
            placeholder={['Select Date', 'Select Date']}
          />
        </div>

        <div className={classes.actionGroup}>
          <Button
            className={classes.searchButton}
            loading={isSearching}
            size="lg"
            fullWidth={isMobile}
            leftSection={!isSearching && <SearchIcon width={11} height={11} />}
            onClick={handleSearch}
          >
            Search
          </Button>
        </div>
      </div>
    </div>
  );
};

export default SearchSection;
