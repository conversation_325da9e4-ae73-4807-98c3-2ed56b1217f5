.container {
  background-color: var(--mantine-color-gray-0);
  border: 1px solid var(--mantine-color-gray-3);
  border-radius: 8px;
  padding: 16px;
  margin-bottom: 16px;
  width: 704px;
  max-width: 100%;
  margin-left: auto;
  margin-right: auto;
  height: 108px; 

  @media (max-width: 768px) {
    width: 100%;
    margin: 0;
    padding: 16px;
    height: 172px;
  }
}

.desktopLayout {
  display: flex;
  flex-direction: row;
  align-items: flex-end;
  gap: 16px;
  
  @media (max-width: 768px) {
    visibility: hidden;
    position: absolute;
    pointer-events: none;
  }
}

.mobileLayout {
  display: flex;
  flex-direction: column;
  gap: 16px;
  
  @media (min-width: 769px) {
    visibility: hidden;
    position: absolute;
    pointer-events: none;
  }
}

.fieldGroup {
  display: flex;
  flex-direction: column;
  gap: 8px;
  flex: 1;

  :global(.mantine-DateInput-input) {
    height: 48px;
  }
}

.actionGroup {
  display: flex;
  gap: 8px;
  align-items: flex-end;
}

.searchButton {
  --button-bg: var(--mantine-color-primary-6);
  --button-color: var(--mantine-color-white);
  --button-bd: var(--mantine-color-primary-6);
  --button-hover: var(--mantine-color-primary-7);
  --button-hover-bd: var(--mantine-color-primary-7);
  --button-active: var(--mantine-color-primary-8);
  --button-active-bd: var(--mantine-color-primary-8);

  border-radius: 6px;
  font-weight: 500;
  min-width: 100px;
}
