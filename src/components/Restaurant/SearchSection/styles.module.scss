.container {
  background-color: var(--mantine-color-gray-0);
  border: 1px solid var(--mantine-color-gray-3);
  border-radius: 8px;
  padding: 24px;
  margin-bottom: 32px;
  width: 704px;
  max-width: 100%;
  margin-left: auto;
  margin-right: auto;

  @media (max-width: 768px) {
    width: 100%;
    margin: 0 0 32px 0;
    padding: 16px;
  }
}

.desktopLayout {
  display: flex;
  flex-direction: row;
  align-items: flex-end;
  gap: 16px;
}

.mobileLayout {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.fieldGroup {
  display: flex;
  flex-direction: column;
  gap: 8px;
  flex: 1;
}

.actionGroup {
  display: flex;
  gap: 8px;
  align-items: flex-end;
}

.searchButton {
  --button-bg: var(--mantine-color-primary-6);
  --button-color: var(--mantine-color-white);
  --button-bd: var(--mantine-color-primary-6);
  --button-hover: var(--mantine-color-primary-7);
  --button-hover-bd: var(--mantine-color-primary-7);
  --button-active: var(--mantine-color-primary-8);
  --button-active-bd: var(--mantine-color-primary-8);

  border-radius: 6px;
  font-weight: 500;
  min-width: 100px;
}
