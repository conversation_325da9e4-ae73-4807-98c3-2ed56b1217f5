'use client';

import { Card, Flex, Group, Skeleton, Stack, useMatches } from '@mantine/core';
import classes from './styles.module.scss';

const RestaurantCardSkeleton = () => {
  const isMobile = useMatches({
    base: true,
    sm: false,
  });

  const imageWidth = isMobile ? 120 : 192;
  const imageHeight = isMobile ? 90 : 144;

  return (
    <Card
      className={classes.card}
      radius="lg"
      shadow="sm"
      withBorder
      style={{ cursor: 'default' }}
    >
      <Flex direction="column" gap={isMobile ? 16 : 24}>
        {/* Main content */}
        <div className={classes.imageContainer}>
          {/* Image */}
          <Skeleton height={imageHeight} width={imageWidth} radius="md" />

          {/* Info */}
          <Stack flex={1} gap={12}>
            {/* Name */}
            <Skeleton height={24} width="70%" radius="sm" />
            <Skeleton height={18} width="50%" radius="sm" />

            {/* Location & Cuisine */}
            <Group gap={isMobile ? 8 : 16}>
              <Group gap={4}>
                <Skeleton height={16} width={16} radius="xl" />
                <Skeleton height={16} width={120} radius="sm" />
              </Group>
              <Group gap={4}>
                <Skeleton height={16} width={16} radius="xl" />
                <Skeleton height={16} width={80} radius="sm" />
              </Group>
            </Group>

            {/* Access */}
            <Group gap={4}>
              <Skeleton height={16} width={16} radius="xl" />
              <Skeleton height={16} width="90%" radius="sm" />
            </Group>

            {/* Meal section */}
            <div className={isMobile ? classes.mealSectionMobile : classes.mealSection}>
              <Group gap={12}>
                <Skeleton height={16} width={100} radius="sm" />
                <Skeleton height={16} width={100} radius="sm" />
              </Group>
            </div>

            {/* Price */}
            {!isMobile && (
              <Group gap={8}>
                <Skeleton height={18} width={80} radius="sm" />
                <Skeleton height={16} width={10} radius="sm" />
                <Skeleton height={18} width={80} radius="sm" />
              </Group>
            )}
          </Stack>
        </div>

        {/* Mobile price */}
        {isMobile && (
          <Group justify="center" gap={8}>
            <Skeleton height={16} width={140} radius="sm" />
            <Skeleton height={16} width={10} radius="sm" />
            <Skeleton height={16} width={140} radius="sm" />
          </Group>
        )}
      </Flex>
    </Card>
  );
};

export default RestaurantCardSkeleton;
