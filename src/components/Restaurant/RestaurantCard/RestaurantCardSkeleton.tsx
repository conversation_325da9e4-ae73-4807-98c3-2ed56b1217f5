'use client';

import { <PERSON>, Card, Flex, Group, Skeleton, Stack, useMatches } from '@mantine/core';
import classes from './styles.module.scss';

const RestaurantCardSkeleton = () => {
  const isMobile = useMatches({
    base: true,
    sm: false,
  });

  const imageWidth = isMobile ? 120 : 192;
  const imageHeight = isMobile ? 90 : 144;

  return (
    <Card
      className={classes.card}
      radius="lg"
      shadow="sm"
      withBorder
      aria-label="Loading restaurant information"
    >
      <Flex
        align="flex-start"
        direction="column"
        gap={isMobile ? 16 : 24}
      >
        {/* Restaurant Image and Info */}
        <div className={classes.imageContainer}>
          {/* Restaurant Image Skeleton */}
          <Skeleton height={imageHeight} width={imageWidth} radius="md" animate />

          {/* Restaurant Info */}
          <Stack flex={1} gap={12}>
            {/* Restaurant Name & Location */}
            <Stack gap={4}>
              {/* Restaurant Name */}
              <Skeleton height={24} width="70%" radius="sm" animate />

              {/* Restaurant Japanese Name */}
              <Skeleton height={18} width="50%" radius="sm" animate />

              <Flex gap={{ base: 8, sm: 16 }} direction={{ base: 'column', sm: 'row' }}>
                {/* Location */}
                <Group gap={4}>
                  <Skeleton height={16} width={16} radius="xl" animate />
                  <Skeleton height={16} width="120px" radius="sm" animate />
                </Group>

                {/* Cuisine Type */}
                <Group gap={4}>
                  <Skeleton height={16} width={16} radius="xl" animate />
                  <Skeleton height={16} width="80px" radius="sm" animate />
                </Group>
              </Flex>
            </Stack>

            {/* Access Information */}
            <Flex gap={4} align="flex-start">
              <Skeleton height={16} width={16} radius="xl" animate />
              <Skeleton height={16} width="90%" radius="sm" animate />
            </Flex>

            {/* Meal Times - Desktop */}
            {!isMobile && (
              <div className={classes.mealSection}>
                <Group gap={16} justify="space-between">
                  <Group gap={12}>
                    {/* Lunch Time */}
                    <Flex align="center" gap={6}>
                      <Skeleton height={16} width={16} radius="xl" animate />
                      <Skeleton height={16} width={100} radius="sm" animate />
                    </Flex>

                    <div className={classes.divider} />

                    {/* Dinner Time */}
                    <Group className={classes.mealTime} gap={6}>
                      <Skeleton height={16} width={16} radius="xl" animate />
                      <Skeleton height={16} width={100} radius="sm" animate />
                    </Group>
                  </Group>
                </Group>
              </div>
            )}

            {/* Price Range - Desktop */}
            {!isMobile && (
              <div className={classes.priceSection}>
                <Flex align="center" gap={8}>
                  <Flex gap={2}>
                    <Skeleton height={18} width={80} radius="sm" animate />
                    <Skeleton height={16} width={60} radius="sm" animate />
                  </Flex>
                  <Skeleton height={16} width={10} radius="sm" animate />
                  <Flex gap={2}>
                    <Skeleton height={18} width={80} radius="sm" animate />
                    <Skeleton height={16} width={60} radius="sm" animate />
                  </Flex>
                </Flex>
              </div>
            )}
          </Stack>
        </div>

        {/* Meal Times and Price Range - Mobile */}
        {isMobile && (
          <>
            <div className={classes.mealSectionMobile} style={{ width: '100%' }}>
              <Flex
                align="center"
                justify="space-around"
                gap={16}
                style={{ flex: '1 1 50%' }}
              >
                {/* Lunch Time - Mobile */}
                <Box>
                  <Flex gap={6} align="center" justify="center">
                    <Skeleton height={16} width={16} radius="xl" animate />
                    <Skeleton height={16} width={70} radius="sm" animate />
                  </Flex>
                </Box>

                {/* Dinner Time - Mobile */}
                <Box>
                  <Flex gap={6} align="center" justify="center">
                    <Skeleton height={16} width={16} radius="xl" animate />
                    <Skeleton height={16} width={70} radius="sm" animate />
                  </Flex>
                </Box>
              </Flex>
            </div>

            {/* Price Range - Mobile */}
            <div className={classes.priceSectionMobile} style={{ width: '100%' }}>
              <Flex justify="center" align="center" gap={8}>
                <Skeleton height={16} width={140} radius="sm" animate />
                <Skeleton height={16} width={10} radius="sm" animate />
                <Skeleton height={16} width={140} radius="sm" animate />
              </Flex>
            </div>
          </>
        )}
      </Flex>
    </Card>
  );
};

export default RestaurantCardSkeleton;
