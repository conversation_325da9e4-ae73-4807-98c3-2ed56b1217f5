.card {
  background: var(--mantine-color-white);
  border: 1px solid var(--mantine-color-gray-3);
  border-radius: 12px;
  padding: 20px;
  cursor: pointer;
  transition: all 0.2s ease;
  width: 704px;
  max-width: 100%;
  margin-left: auto;
  margin-right: auto;

  &:hover {
    border-color: var(--mantine-color-primary-4);
    box-shadow: 0 4px 12px rgba(12, 13, 13, 0.1);
    transform: translateY(-2px);
  }
}

.imageContainer {
  display: flex;
  gap:16px;
  @media (max-width: 768px) {
    gap:12px;
  }
}

.image {
  border-radius: 8px;
  object-fit: cover;
}

.restaurantName {
  color: var(--mantine-color-primary-6);
  font-size: 18px;
  font-weight: 700;
  line-height: 1.4;
  font-family: 'Merriweather', serif !important;
  margin: 0;
}

.restaurantNameJp {
  color: var(--mantine-color-gray-7);
  font-size: 14px;
  font-weight: 400;
  line-height: 1.4;
  margin: 0;
}

.locationIcon {
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
}

.location {
  color: var(--mantine-color-gray-9);
  font-size: 12px;
  font-weight: 400;
  line-height: 1.4;
  
}

.cuisineBadge {
  font-size: 12px;
  color: var(--mantine-color-gray-9);
  font-weight: 400;
}

.access {
  color: var(--mantine-color-gray-9);
  font-size: 12px;
  font-weight: 400;
  line-height: '16px';
  margin: 0;
}

.accessLabel {
  color: var(--mantine-color-gray-8);
  font-weight: 500;
}

.mealSection {
  display: none;
  @media (min-width: 769px) {
    display: block;
    background-color: var(--mantine-color-gray-1);
    border-radius: 8px;
    padding: 12px 16px;
    margin-top: 8px;
  }
}
.mealSectionMobile {
  display: none;
  @media (max-width: 768px) {
    display: block;
    background-color: var(--mantine-color-gray-1);
    padding: 4px 24px;
    border-radius: 8px;
  }
}
.priceSection {
  display: block;
  @media (max-width: 768px) {
    display: none;
  }
}
.priceSectionMobile {
  display: block;
  @media (min-width: 769px) {
    display: none;
  }
}
.priceRange {
  margin-top: 8px;
}
.priceRange

.mealTime {
  align-items: center;
}
.divider {
  width: 1px;
  height: 20px;
  background-color: var(--mantine-color-gray-3);
  flex-shrink: 0;
}

.sunIcon,
.moonIcon {
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
}

.mealLabel {
  color: var(--mantine-color-gray-7);
  font-size: 13px;
  font-weight: 500;
  margin: 0;
}

.price {
  color: var(--mantine-color-gray-9);
  font-size: 14px;
  font-weight: 700;
  line-height: '20px';
  margin: 0;
  text-align: right;
}
.priceUsd {
  color: var(--mantine-color-gray-6);
  font-size: 12px;
  font-weight: 400;
  line-height: '16px';
  margin: 0;
  text-align: right;
}

/* Mobile specific styles */
@media (max-width: 768px) {
  .card {
    padding: 16px;
    width: 100%;
    margin: 0;
  }

  .restaurantName {
    font-size: 16px;
  }

  .mealSection {
    padding: 10px 12px;
  }

  .price {
    font-size: 12px;
  }
}
