.container {
  padding: 24px 0;
  margin-top: 32px;
  width: 704px;
  max-width: 100%;
  margin-left: auto;
  margin-right: auto;
}

.itemsInfo {
  color: var(--mantine-color-gray-7);
  font-size: 14px;
  font-weight: 400;
  line-height: 1.4;
}

.control {
  --pagination-color: var(--mantine-color-gray-5);
  --pagination-bg: transparent;
  --pagination-bd: transparent;
  --pagination-hover: transparent;
  --pagination-hover-color: var(--mantine-color-gray-6);
  --pagination-hover-bd: transparent;

  border-radius: 6px;
  font-size: 14px;
  font-weight: 500;
  height: 32px;
  min-width: 32px;
  transition: all 0.2s ease;
  border: none;
  background-color: transparent;

  // Active state - matches Figma design with #BC923A background and white text
  &[data-active] {
    --pagination-color: var(--mantine-color-white);
    --pagination-bg: #BC923A;
    --pagination-bd: #BC923A;
    --pagination-hover: #9C7A30;
    --pagination-hover-bd: #9C7A30;

    background-color: var(--pagination-bg) !important;
    color: var(--pagination-color) !important;
    border-color: var(--pagination-bd) !important;
    border: 1px solid var(--pagination-bd);
    font-weight: 700;

    &:hover {
      background-color: var(--pagination-hover) !important;
      border-color: var(--pagination-hover-bd) !important;
    }
  }

  // Inactive state - no borders, no background
  &:not([data-active]) {
    border: none;
    background-color: transparent;

    &:hover {
      background-color: transparent;
      color: var(--pagination-hover-color);
      border: none;
    }
  }

  // Disabled state (for edge controls when not available)
  &:disabled {
    --pagination-color: var(--mantine-color-gray-4);
    --pagination-bg: transparent;
    --pagination-bd: transparent;
    cursor: not-allowed;
    border: none;
    background-color: transparent;

    &:hover {
      --pagination-bg: transparent;
      --pagination-bd: transparent;
      background-color: transparent !important;
      border: none !important;
    }
  }
}

.dots {
  color: var(--mantine-color-gray-5);
  font-size: 14px;
  font-weight: 500;
}

/* Mobile specific adjustments */
@media (max-width: 768px) {
  .container {
    padding: 20px 0;
    margin-top: 24px;
    width: 100%;
    margin-left: 0;
    margin-right: 0;
  }

  .control {
    height: 32px;
    border: none;
    min-width: 32px;
    font-size: 13px;
    background-color: transparent;
  }

  .itemsInfo {
    font-size: 13px;
  }
}
