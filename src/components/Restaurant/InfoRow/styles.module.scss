.infoTable {
  background-color: white;
  border: none;
  border-radius: 4px;
  padding: 24px;
  overflow: hidden;

  @media (max-width: 768px) {
    padding: 0;
  }
}

.infoRow {
  display: flex;
  border: 1px solid #EAEAEA;
  border-bottom: none;

  &:first-child {
    border-top: 1px solid #EAEAEA; // Add top border for first item on desktop
  }

  &:last-child {
    border-bottom: 1px solid #EAEAEA; // Add bottom border for last item on desktop
  }

  @media (max-width: 768px) {
    border-left: none; // Remove left border on mobile
    border-right: none; // Remove right border on mobile
    
    &:first-child {
      border-top: none; // Remove top border on mobile
    }

    &:last-child {
      border-bottom: none; // Remove bottom border on mobile
    }
  }
}

.infoLabel {
  width: 220px;
  padding: 12px 16px;
  background-color: white;
  border-right: 1px solid #EAEAEA;
  font-size: 14px;
  font-weight: 500;
  color: #828282;

  @media (max-width: 768px) {
    width: 110px;
    font-size: 12px;
    border-right: none; // Remove vertical border on mobile
  }
}

.infoValue {
  flex: 1;
  padding: 12px 16px;
  font-size: 14px;
  color: #111D2F;

  @media (max-width: 768px) {
    font-size: 14px;
    line-height: 22px;
    font-weight: 400;
  }
}

.mapContainer {
  width: 100%;
  height: 218px;
  background-color: #f5f5f5;
  border-radius: 4px;
  overflow: hidden;

  iframe {
    width: 100%;
    height: 100%;
    border: none;
  }
}

.servingTime {
  display: flex;
  flex-direction: row;
  align-items: center;
  gap: 8px; // Add spacing between elements on desktop

  @media (max-width: 768px) {
    display: flex;
    flex-direction: column;
    align-items: flex-start; // Align left on mobile
    gap: 4px; // Small gap between lines on mobile
  }
}

.servingTimeSeparator {
  display: inline;

  @media (max-width: 768px) {
    display: inline; // Show separator on mobile too
  }
} 