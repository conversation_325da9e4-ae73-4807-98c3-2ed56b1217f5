.leftSidebar {
  position: fixed;
  top: 0; // Will be calculated dynamically based on hero image height
  left: 32px; // 32px margin from left
  width: 240px;
  height: auto;
  background: transparent; // Remove background
  border-right: none; // Remove border
  z-index: 90;
  transform: translateX(0); // Always visible
  transition: top 0.3s ease;
  overflow: visible;
  
  &.visible {
    transform: translateX(0);
  }
  
  // Hide on mobile and tablet
  @media (max-width: 1024px) {
    display: none;
  }
}

.sidebarContent {
  padding: 24px 0;
  display: flex;
  flex-direction: column;
  gap: 4px;
  background: transparent; // Ensure no background
}

.navItem {
  background: none;
  border: none;
  padding: 8px 12px;
  font-family: 'Roboto', sans-serif;
  font-size: 16px;
  font-weight: 500;
  color: #111D2F;
  cursor: pointer;
  transition: all 0.2s ease;
  text-align: left;
  width: 100%;
  position: relative;
  border-left: 3px solid transparent;
  
  &:hover {
    background-color: #FEF3E2;
    color: #BC923A;
  }
  
  &.active {
    color: #111D2F;
    border-left-color: #BC923A;
    background-color: rgba(188, 146, 58, 0.05);
  }
  
  &:focus-visible {
    outline: 2px solid #BC923A;
    outline-offset: -2px;
  }
}

// Scrollbar styling
.leftSidebar::-webkit-scrollbar {
  width: 4px;
}

.leftSidebar::-webkit-scrollbar-track {
  background: #f1f1f1;
}

.leftSidebar::-webkit-scrollbar-thumb {
  background: #BC923A;
  border-radius: 2px;
}

.leftSidebar::-webkit-scrollbar-thumb:hover {
  background: #A8832F;
}
