'use client';

import { Box } from '@mantine/core';
import { useEffect, useState } from 'react';
import classes from './styles.module.scss';

type NavigationItem = {
  id: string;
  label: string;
  sectionId: string;
};

const navigationItems: NavigationItem[] = [
  { id: 'about-restaurant', label: 'About Restaurant', sectionId: 'about-restaurant' },
  { id: 'about-dish', label: 'About Dish', sectionId: 'about-dish' },
  { id: 'about-chef', label: 'About Chef', sectionId: 'about-chef' },
  { id: 'selling-point', label: 'Selling Point', sectionId: 'selling-point' },
  { id: 'course-section', label: 'Course', sectionId: 'course-section' },
  { id: 'restaurant-information', label: 'Restaurant Info', sectionId: 'restaurant-information' },
];

const LeftSidebar = () => {
  const [activeSection, setActiveSection] = useState('about-restaurant');
  const [topPosition, setTopPosition] = useState(0);

  const scrollToSection = (sectionId: string) => {
    const element = document.getElementById(sectionId);
    if (element) {
      const headerOffset = 120; // Account for header height + some padding
      const elementPosition = element.offsetTop;
      const offsetPosition = elementPosition - headerOffset;

      window.scrollTo({
        top: offsetPosition,
        behavior: 'smooth',
      });
    }
  };

  useEffect(() => {
    let ticking = false;

    const updatePosition = () => {
      const heroSection = document.querySelector('.hero-section') as HTMLElement;
      const headerHeight = 72; // Desktop header height

      if (heroSection) {
        const heroRect = heroSection.getBoundingClientRect();
        const heroBottom = heroRect.bottom;

        // Position below hero image or below header if scrolled past hero
        const newTopPosition = Math.max(headerHeight, heroBottom);
        setTopPosition(newTopPosition);
      }
    };

    const handleScroll = () => {
      if (!ticking) {
        requestAnimationFrame(() => {
          updatePosition();

          // Determine active section
          const sections = navigationItems.map(item => item.sectionId);
          const headerHeight = 72;
          const scrollOffset = headerHeight + 60; // Header + navigation offset
          const scrollPosition = window.scrollY + scrollOffset;

          for (let i = sections.length - 1; i >= 0; i--) {
            const sectionId = sections[i];
            if (!sectionId) {
              continue;
            }

            const section = document.getElementById(sectionId);
            if (section && section.offsetTop <= scrollPosition) {
              setActiveSection(sectionId);
              break;
            }
          }

          ticking = false;
        });
        ticking = true;
      }
    };

    window.addEventListener('scroll', handleScroll, { passive: true });
    window.addEventListener('resize', updatePosition, { passive: true });
    updatePosition(); // Calculate initial position
    handleScroll(); // Check initial state

    return () => {
      window.removeEventListener('scroll', handleScroll);
      window.removeEventListener('resize', updatePosition);
    };
  }, []);

  // Only show on desktop
  if (typeof window !== 'undefined' && window.innerWidth <= 1024) {
    return null;
  }

  return (
    <Box
      className={`${classes.leftSidebar} ${classes.visible}`}
      component="nav"
      role="navigation"
      aria-label="Page sections navigation"
      style={{ top: `${topPosition}px` }}
    >
      <div className={classes.sidebarContent}>
        {navigationItems.map(item => (
          <button
            key={item.id}
            className={`${classes.navItem} ${activeSection === item.sectionId ? classes.active : ''}`}
            onClick={() => scrollToSection(item.sectionId)}
            aria-label={`Navigate to ${item.label} section`}
            type="button"
          >
            {item.label}
          </button>
        ))}
      </div>
    </Box>
  );
};

export default LeftSidebar;
