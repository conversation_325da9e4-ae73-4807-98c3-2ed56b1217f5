.floatingButton {
  position: fixed;
  bottom: 24px;
  right: 16px;
  z-index: 100;
  
  // Button styling based on brand design system
  --button-bg: #BC923A; // Primary brand color
  --button-color: #FFFFFF;
  --button-bd: #BC923A;
  --button-hover: #A8832F; // Darker shade for hover
  --button-hover-bd: #A8832F;
  --button-active: #9C7A30; // Even darker for active
  --button-active-bd: #9C7A30;
  
  // Typography matching Figma design
  font-family: 'Merriweather', serif;
  font-weight: 400;
  font-size: 14px;
  line-height: 1.43;
  
  // Dimensions and spacing
  height: 48px;
  padding: 12px 16px;
  border-radius: 6px;
  min-width: 150px;
  
  // Shadow for floating effect
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.15), 0 2px 8px rgba(0, 0, 0, 0.1);
  
  // Animation and transitions
  transform: translateY(100px);
  opacity: 0;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  
  &.visible {
    transform: translateY(0);
    opacity: 1;
  }
  
  // Hover effects
  &:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(0, 0, 0, 0.2), 0 3px 10px rgba(0, 0, 0, 0.15);
  }
  
  &:active {
    transform: translateY(0);
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
  }
  
  // Focus state for accessibility
  &:focus-visible {
    outline: 2px solid #BC923A;
    outline-offset: 2px;
  }
  
  // Hide on desktop (1025px+) where left sidebar is available
  @media (min-width: 1025px) {
    display: none;
  }
  
  // Responsive positioning for different mobile sizes
  @media (max-width: 480px) {
    right: 12px;
    bottom: 20px;
    min-width: 140px;
    font-size: 13px;
    padding: 10px 14px;
    height: 44px;
  }
  
  // Ensure button is easily tappable on mobile
  @media (max-width: 768px) {
    // Minimum touch target size for accessibility
    min-height: 44px;
    min-width: 44px;
  }
}
