.floatingButton {
  position: fixed;
  bottom: 24px;
  left: 50%;
  transform: translateX(-50%) translateY(100px);
  z-index: 100;
  
  // Fixed dimensions as specified
  height: 48px;
  width: 284px;
  
  // Button styling based on brand design system
  --button-bg: #BC923A; // Primary brand color
  --button-color: #FFFFFF;
  --button-bd: #BC923A;
  --button-hover: #A8832F; // Darker shade for hover
  --button-hover-bd: #A8832F;
  --button-active: #9C7A30; // Even darker for active
  --button-active-bd: #9C7A30;
  
  // Typography matching Figma design
  font-family: 'Merriweather', serif;
  font-weight: 400;
  font-size: 14px;
  line-height: 1.43;
  
  // Spacing and appearance
  padding: 12px 16px;
  border-radius: 6px;
  
  // Shadow for floating effect
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.15), 0 2px 8px rgba(0, 0, 0, 0.1);
  
  // Initially hidden with smooth transitions
  opacity: 0;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  
  &.visible {
    transform: translateX(-50%) translateY(0);
    opacity: 1;
  }
  
  // Hover effects
  &:hover {
    transform: translateX(-50%) translateY(-2px);
    box-shadow: 0 6px 20px rgba(0, 0, 0, 0.2), 0 3px 10px rgba(0, 0, 0, 0.15);
    
    &.visible {
      transform: translateX(-50%) translateY(-2px);
    }
  }
  
  &:active {
    transform: translateX(-50%) translateY(0);
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
    
    &.visible {
      transform: translateX(-50%) translateY(0);
    }
  }
  
  // Focus state for accessibility
  &:focus-visible {
    outline: 2px solid #BC923A;
    outline-offset: 2px;
  }
  
  // Hide on desktop (769px+) where course section is easily accessible
  @media (min-width: 769px) {
    display: none;
  }
  
  // Responsive adjustments for smaller screens
  @media (max-width: 320px) {
    width: calc(100% - 32px);
    left: 16px;
    transform: translateY(100px);
    
    &.visible {
      transform: translateY(0);
    }
    
    &:hover {
      transform: translateY(-2px);
      
      &.visible {
        transform: translateY(-2px);
      }
    }
    
    &:active {
      transform: translateY(0);
      
      &.visible {
        transform: translateY(0);
      }
    }
  }
}
