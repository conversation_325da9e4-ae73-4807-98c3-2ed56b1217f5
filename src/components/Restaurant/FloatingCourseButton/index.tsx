'use client';

import { Button } from '@mantine/core';
import { useEffect, useState } from 'react';
import classes from './styles.module.scss';

const FloatingCourseButton = () => {
  const [isMobile, setIsMobile] = useState(false);

  const scrollToCourseSection = () => {
    const courseSection = document.getElementById('course-section');
    if (courseSection) {
      const headerOffset = 80; // Account for header height + some padding
      const elementPosition = courseSection.offsetTop;
      const offsetPosition = elementPosition - headerOffset;

      window.scrollTo({
        top: offsetPosition,
        behavior: 'smooth',
      });
    }
  };

  useEffect(() => {
    // Check if device is mobile/tablet
    const checkMobile = () => {
      setIsMobile(window.innerWidth <= 768);
    };

    checkMobile();
    window.addEventListener('resize', checkMobile);

    return () => {
      window.removeEventListener('resize', checkMobile);
    };
  }, []);

  // Don't render on desktop
  if (!isMobile) {
    return null;
  }

  return (
    <Button
      className={classes.floatingButton}
      onClick={scrollToCourseSection}
      size="md"
      variant="filled"
      color="primary"
      aria-label="View our courses section"
    >
      View our courses
    </Button>
  );
};

export default FloatingCourseButton;
