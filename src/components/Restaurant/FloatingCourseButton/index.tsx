'use client';

import { Button } from '@mantine/core';
import { useEffect, useState } from 'react';
import classes from './styles.module.scss';

const FloatingCourseButton = () => {
  const [isVisible, setIsVisible] = useState(false);
  const [isMobile, setIsMobile] = useState(false);

  const scrollToCourseSection = () => {
    const courseSection = document.getElementById('course-section');
    if (courseSection) {
      const headerOffset = 80; // Account for header height + some padding
      const elementPosition = courseSection.offsetTop;
      const offsetPosition = elementPosition - headerOffset;

      window.scrollTo({
        top: offsetPosition,
        behavior: 'smooth',
      });
    }
  };

  useEffect(() => {
    // Check if device is mobile/tablet
    const checkMobile = () => {
      setIsMobile(window.innerWidth <= 768);
    };

    checkMobile();
    window.addEventListener('resize', checkMobile);

    let ticking = false;

    const handleScroll = () => {
      if (!ticking) {
        requestAnimationFrame(() => {
          // Only show on mobile devices
          if (window.innerWidth > 768) {
            setIsVisible(false);
            ticking = false;
            return;
          }

          const heroSection = document.querySelector('.hero-section') as HTMLElement;
          const courseSection = document.getElementById('course-section');

          if (heroSection && courseSection) {
            const heroBottom = heroSection.getBoundingClientRect().bottom;
            const courseSectionRect = courseSection.getBoundingClientRect();
            const headerHeight = 72; // Mobile header height

            // Check if hero section has been scrolled past
            const heroScrolledPast = heroBottom <= headerHeight;

            // Check if course section is visible on screen
            const courseSectionVisible = courseSectionRect.top < window.innerHeight
              && courseSectionRect.bottom > 0;

            // Show button only if:
            // 1. Hero section has been scrolled past
            // 2. Course section is NOT visible on screen
            const shouldShow = heroScrolledPast && !courseSectionVisible;
            setIsVisible(shouldShow);
          }

          ticking = false;
        });
        ticking = true;
      }
    };

    window.addEventListener('scroll', handleScroll, { passive: true });
    handleScroll(); // Check initial state

    return () => {
      window.removeEventListener('scroll', handleScroll);
      window.removeEventListener('resize', checkMobile);
    };
  }, []);

  // Don't render on desktop
  if (!isMobile) {
    return null;
  }

  return (
    <Button
      className={`${classes.floatingButton} ${isVisible ? classes.visible : ''}`}
      onClick={scrollToCourseSection}
      size="md"
      variant="filled"
      color="primary"
      aria-label="View our courses section"
    >
      View our courses
    </Button>
  );
};

export default FloatingCourseButton;
