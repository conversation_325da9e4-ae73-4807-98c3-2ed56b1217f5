import classes from './styles.module.scss';

type SectionHeaderProps = {
  title: string;
  subtitle?: string;
};

const SectionHeader = ({ title, subtitle }: SectionHeaderProps) => (
  <div className={classes.sectionHeader}>
    <h2 className={classes.sectionTitle}>{title}</h2>
    {subtitle && (
      <p className={classes.sectionSubtitle}>{subtitle}</p>
    )}
    <div className={classes.sectionDivider} />
  </div>
);

export default SectionHeader;
