/* eslint-disable unused-imports/no-unused-vars */
'use client';

import { Image, Text } from '@mantine/core';
import { useEffect, useState } from 'react';

import classes from './styles.module.scss';

export type ImageGalleryProps = {
  images: string[];
  altPrefix?: string;
  maxImages?: number;
  showOverlay?: boolean;
  onImageClick?: (imageUrl: string) => void;
  breakpoint?: number;
};

export type ImageGalleryConfig = {
  desktop: {
    single: { width: number; height: number };
    double: { width: number; height: number };
    triple: { width: number; height: number };
    quad: {
      featured: { width: number; height: number };
      grid: { width: number; height: number };
    };
    quintuple: {
      row1: { width: number; height: number };
      row2: { width: number; height: number };
    };
    sextuple: { width: number; height: number };
  };
  mobile: {
    single: { width: string; height: number };
    grid: { width: number; height: number };
  };
  gap: number;
  breakpoint: number;
};

const defaultConfig: ImageGalleryConfig = {
  desktop: {
    single: { width: 704, height: 554 },
    double: { width: 348, height: 261 },
    triple: { width: 229, height: 172 },
    quad: {
      featured: { width: 704, height: 528 },
      grid: { width: 229, height: 172 },
    },
    quintuple: {
      row1: { width: 348, height: 261 },
      row2: { width: 229, height: 172 },
    },
    sextuple: { width: 229, height: 172 },
  },
  mobile: {
    single: { width: '100%', height: 193 },
    grid: { width: 111, height: 83 },
  },
  gap: 8,
  breakpoint: 768,
};

const ImageGallery = ({
  images,
  altPrefix = 'Image',
  maxImages = 6,
  showOverlay = true,
  onImageClick,
  breakpoint = 768,
}: ImageGalleryProps) => {
  const [selectedImage, setSelectedImage] = useState<string | null>(null);
  const [isMobile, setIsMobile] = useState(false);

  // Handle responsive behavior
  useEffect(() => {
    const checkMobile = () => {
      setIsMobile(window.innerWidth <= breakpoint);
    };

    checkMobile();
    window.addEventListener('resize', checkMobile);
    return () => window.removeEventListener('resize', checkMobile);
  }, [breakpoint]);

  const handleImageClick = (imageUrl: string) => {
    if (onImageClick) {
      onImageClick(imageUrl);
    } else {
      setSelectedImage(imageUrl);
    }
  };

  const closeModal = () => {
    setSelectedImage(null);
  };

  // Handle escape key to close modal
  useEffect(() => {
    const handleEscape = (e: KeyboardEvent) => {
      if (e.key === 'Escape' && selectedImage) {
        closeModal();
      }
    };

    document.addEventListener('keydown', handleEscape);
    return () => document.removeEventListener('keydown', handleEscape);
  }, [selectedImage]);

  if (!images.length) {
    return null;
  }

  const displayImages = images.slice(0, maxImages);
  const imageCount = displayImages.length;

  const renderImageLayout = () => {
    // Single image layout
    if (imageCount === 1) {
      return (
        <div className={classes.singleImageLayout}>
          <div className={classes.singleImageContainer}>
            <Image
              src={displayImages[0]}
              alt={`${altPrefix} 1`}
              className={classes.galleryImage}
              onClick={() => handleImageClick(displayImages[0] || '')}
              style={{ cursor: 'pointer' }}
            />
          </div>
        </div>
      );
    }

    // Two images layout - single row
    if (imageCount === 2) {
      return (
        <div className={classes.doubleImageLayout}>
          {displayImages.map((img, idx) => (
            <div key={`img-${idx}`} className={classes.doubleImageContainer}>
              <Image
                src={img}
                alt={`${altPrefix} ${idx + 1}`}
                className={classes.galleryImage}
                onClick={() => handleImageClick(img)}
                style={{ cursor: 'pointer' }}
              />
            </div>
          ))}
        </div>
      );
    }

    // Three images layout - single row
    if (imageCount === 3) {
      return (
        <div className={classes.tripleImageLayout}>
          {displayImages.map((img, idx) => (
            <div key={`img-${idx}`} className={classes.tripleImageContainer}>
              <Image
                src={img}
                alt={`${altPrefix} ${idx + 1}`}
                className={classes.galleryImage}
                onClick={() => handleImageClick(img)}
                style={{ cursor: 'pointer' }}
              />
            </div>
          ))}
        </div>
      );
    }

    // Four images layout - featured + 3 grid
    if (imageCount === 4) {
      return (
        <div className={classes.quadImageLayout}>
          {/* Featured image - full width row 1 */}
          <div className={classes.featuredImageContainer}>
            <Image
              src={displayImages[0]}
              alt={`${altPrefix} 1`}
              className={classes.galleryImage}
              onClick={() => handleImageClick(displayImages[0] || '')}
              style={{ cursor: 'pointer' }}
            />
          </div>
          {/* Grid images - row 2 */}
          <div className={classes.gridRow}>
            {displayImages.slice(1, 4).map((img, idx) => (
              <div key={`img-${idx + 1}`} className={classes.gridImageContainer}>
                <Image
                  src={img}
                  alt={`${altPrefix} ${idx + 2}`}
                  className={classes.galleryImage}
                  onClick={() => handleImageClick(img)}
                  style={{ cursor: 'pointer' }}
                />
              </div>
            ))}
          </div>
        </div>
      );
    }

    // Five images layout - 2 + 3 grid
    if (imageCount === 5) {
      return (
        <div className={classes.quintupleImageLayout}>
          {/* Row 1 - 2 images */}
          <div className={classes.doubleRow}>
            {displayImages.slice(0, 2).map((img, idx) => (
              <div key={`img-${idx}`} className={classes.doubleRowImageContainer}>
                <Image
                  src={img}
                  alt={`${altPrefix} ${idx + 1}`}
                  className={classes.galleryImage}
                  onClick={() => handleImageClick(img)}
                  style={{ cursor: 'pointer' }}
                />
              </div>
            ))}
          </div>
          {/* Row 2 - 3 images */}
          <div className={classes.tripleRow}>
            {displayImages.slice(2, 5).map((img, idx) => (
              <div key={`img-${idx + 2}`} className={classes.tripleRowImageContainer}>
                <Image
                  src={img}
                  alt={`${altPrefix} ${idx + 3}`}
                  className={classes.galleryImage}
                  onClick={() => handleImageClick(img)}
                  style={{ cursor: 'pointer' }}
                />
              </div>
            ))}
          </div>
        </div>
      );
    }

    // Six images layout - 2 rows of 3 each
    return (
      <div className={classes.sextupleImageLayout}>
        {/* Row 1 - 3 images */}
        <div className={classes.tripleRow}>
          {displayImages.slice(0, 3).map((img, idx) => (
            <div key={`img-${idx}`} className={classes.tripleRowImageContainer}>
              <Image
                src={img}
                alt={`${altPrefix} ${idx + 1}`}
                className={classes.galleryImage}
                onClick={() => handleImageClick(img)}
                style={{ cursor: 'pointer' }}
              />
            </div>
          ))}
        </div>
        {/* Row 2 - 3 images */}
        <div className={classes.tripleRow}>
          {displayImages.slice(3, 6).map((img, idx) => (
            <div key={`img-${idx + 3}`} className={classes.tripleRowImageContainer}>
              <Image
                src={img}
                alt={`${altPrefix} ${idx + 4}`}
                className={classes.galleryImage}
                onClick={() => handleImageClick(img)}
                style={{ cursor: 'pointer' }}
              />
              {/* Show overlay on last image if there are more images */}
              {idx === 2 && images.length > maxImages && showOverlay && (
                <button
                  type="button"
                  className={classes.imageOverlay}
                  onClick={() => handleImageClick(img)}
                  aria-label={`View all ${images.length} images`}
                >
                  <Text className={classes.overlayText}>
                    +
                    {images.length - maxImages}
                    {' '}
                    more
                  </Text>
                </button>
              )}
            </div>
          ))}
        </div>
      </div>
    );
  };

  return (
    <>
      <div className={classes.imageGallery}>
        {renderImageLayout()}
      </div>

      {/* Image modal for full-size viewing */}
      {selectedImage && !onImageClick && (
        <div
          className={classes.imageModal}
          role="dialog"
          aria-modal="true"
          aria-label="Image viewer"
        >
          <div
            // className={classes.modalContent}
            // onClick={e => e.stopPropagation()}
          >
            <button
              type="button"
              className={classes.closeButton}
              onClick={closeModal}
              aria-label="Close image viewer"
            >
              ×
            </button>
            <Image
              src={selectedImage}
              alt="Full size image"
              className={classes.modalImage}
            />
          </div>
          <button
            type="button"
            className={classes.modalBackdrop}
            onClick={closeModal}
            aria-label="Close image viewer"
            style={{
              position: 'absolute',
              top: 0,
              left: 0,
              width: '100%',
              height: '100%',
              background: 'transparent',
              border: 'none',
              cursor: 'pointer',
              zIndex: -1,
            }}
          />
        </div>
      )}
    </>
  );
};

export default ImageGallery;
