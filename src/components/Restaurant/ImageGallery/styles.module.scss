// ImageGallery Component Styles
.imageGallery {
  width: 100%;
  max-width: 704px;
  margin: 0 auto;
}

.galleryImage {
  width: 100%;
  height: 100%;
  object-fit: cover;
  border-radius: 4px;
  transition: transform 0.2s ease;

  &:hover {
    transform: scale(1.02);
  }
}

// Single image layout (1 image)
.singleImageLayout {
  width: 100%;
}

.singleImageContainer {
  width: 704px;
  height: 554px;
  border-radius: 4px;
  overflow: hidden;

  @media (max-width: 768px) {
    width: 100%;
    height: 193px;
  }
}

// Double image layout (2 images)
.doubleImageLayout {
  display: flex;
  gap: 8px;
  width: 100%;
}

.doubleImageContainer {
  width: 348px;
  height: 261px;
  border-radius: 4px;
  overflow: hidden;
  flex: 1;

  @media (max-width: 768px) {
    width: 111px;
    height: 83px;
  }
}

// Triple image layout (3 images)
.tripleImageLayout {
  display: flex;
  gap: 8px;
  width: 100%;
}

.tripleImageContainer {
  width: 229px;
  height: 172px;
  border-radius: 4px;
  overflow: hidden;
  flex: 1;

  @media (max-width: 768px) {
    width: 111px;
    height: 83px;
  }
}

// Quad image layout (4 images)
.quadImageLayout {
  display: flex;
  flex-direction: column;
  gap: 8px;
  width: 100%;
}

.featuredImageContainer {
  width: 704px;
  height: 528px;
  border-radius: 4px;
  overflow: hidden;

  @media (max-width: 768px) {
    width: 100%;
    height: 193px;
  }
}

.gridRow {
  display: flex;
  gap: 8px;
  width: 100%;
}

.gridImageContainer {
  width: 229px;
  height: 172px;
  border-radius: 4px;
  overflow: hidden;
  flex: 1;

  @media (max-width: 768px) {
    width: 111px;
    height: 83px;
  }
}

// Quintuple image layout (5 images)
.quintupleImageLayout {
  display: flex;
  flex-direction: column;
  gap: 8px;
  width: 100%;
}

.doubleRow {
  display: flex;
  gap: 8px;
  width: 100%;
}

.doubleRowImageContainer {
  width: 348px;
  height: 261px;
  border-radius: 4px;
  overflow: hidden;
  flex: 1;

  @media (max-width: 768px) {
    width: 111px;
    height: 83px;
  }
}

.tripleRow {
  display: flex;
  gap: 8px;
  width: 100%;
}

.tripleRowImageContainer {
  width: 229px;
  height: 172px;
  border-radius: 4px;
  overflow: hidden;
  flex: 1;
  position: relative;

  @media (max-width: 768px) {
    width: 111px;
    height: 83px;
  }
}

// Sextuple image layout (6 images)
.sextupleImageLayout {
  display: flex;
  flex-direction: column;
  gap: 8px;
  width: 100%;
}

// Image overlay for "more images" indicator
.imageOverlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.6);
  border: none;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 4px;
  transition: background-color 0.2s ease;

  &:hover {
    background: rgba(0, 0, 0, 0.7);
  }
}

.overlayText {
  color: white;
  font-size: 16px;
  font-weight: 600;
  text-align: center;

  @media (max-width: 768px) {
    font-size: 12px;
  }
}

// Modal styles
.imageModal {
  position: fixed;
  top: 0;
  left: 0;
  width: 100vw;
  height: 100vh;
  background: rgba(0, 0, 0, 0.9);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  padding: 20px;
}

.modalContent {
  position: relative;
  max-width: 90vw;
  max-height: 90vh;
  background: white;
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3);
}

.modalImage {
  width: 100%;
  height: 100%;
  object-fit: contain;
  max-width: 90vw;
  max-height: 90vh;
}

.closeButton {
  position: absolute;
  top: 10px;
  right: 15px;
  background: rgba(0, 0, 0, 0.7);
  color: white;
  border: none;
  border-radius: 50%;
  width: 40px;
  height: 40px;
  font-size: 24px;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1001;
  transition: background-color 0.2s ease;

  &:hover {
    background: rgba(0, 0, 0, 0.9);
  }
}

.modalBackdrop {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: transparent;
  border: none;
  cursor: pointer;
  z-index: -1;
}

// Mobile responsive adjustments
@media (max-width: 768px) {
  .imageGallery {
    max-width: 100%;
    padding: 0;
  }

  // Ensure all mobile layouts use consistent sizing
  .doubleImageContainer,
  .tripleImageContainer,
  .gridImageContainer,
  .doubleRowImageContainer,
  .tripleRowImageContainer {
    width: 111px;
    height: 83px;
  }

  // Single image takes full width on mobile
  .singleImageContainer,
  .featuredImageContainer {
    width: 100%;
    height: 193px;
  }

  .modalContent {
    max-width: 95vw;
    max-height: 95vh;
    margin: 10px;
  }

  .closeButton {
    width: 35px;
    height: 35px;
    font-size: 20px;
    top: 5px;
    right: 10px;
  }
}
