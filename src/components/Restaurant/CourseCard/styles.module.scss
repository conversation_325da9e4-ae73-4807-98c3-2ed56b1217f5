.courseCard {
  background-color: white;
  border: 1px solid var(--mantine-color-gray-3);
  border-radius: 8px;
  padding: 24px;
  margin-bottom: 16px;

  @media (max-width: 768px) {
    padding: 16px;
  }
}

.courseHeader {
  background-color: var(--mantine-color-gray-1);
  border-radius: 6px;
  padding: 8px 12px;
  display: inline-block;
}

.courseMealTime {
  display: flex;
  align-items: center;
  gap: 6px;
  font-size: 12px;
  color: var(--mantine-color-gray-7);
}

.courseMealLabel {
  font-weight: 500;
}

.courseMealValue {
  font-weight: 400;
}

.courseTitle {
  font-size: 20px;
  font-weight: 700;
  color: var(--mantine-color-gray-9);
  line-height: 1.4;

  @media (max-width: 768px) {
    font-size: 18px;
  }
}

.courseDuration {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 14px;
  color: var(--mantine-color-gray-7);
}

.coursePriceSection {
  display: flex;
  justify-content: space-between;
  align-items: flex-end;
  gap: 16px;
  margin-top: 16px;

  @media (max-width: 768px) {
    flex-direction: column;
    align-items: stretch;
    gap: 12px;
  }
}

.coursePriceLabel {
  font-size: 12px;
  color: var(--mantine-color-gray-6);
  font-weight: 500;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.coursePrice {
  font-size: 20px;
  font-weight: 700;
  color: #111D2F;
}

.coursePriceUsd {
  font-size: 14px;
  color: #828282;
}

.reserveButton {
  background-color: #BC923A;
  color: white;
  border-radius: 4px;
  padding: 12px 16px;
  font-size: 14px;
  font-weight: 500;
  width: 200px;
  border: none;
  cursor: pointer;

  &:hover {
    background-color: #A8832F;
  }

  @media (max-width: 768px) {
    width: 100%;
  }
} 