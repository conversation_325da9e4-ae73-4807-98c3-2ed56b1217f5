/* eslint-disable regexp/no-super-linear-backtracking */
export const REG_EXP = {
  PASSWORD: /^(?=.*[A-Z])(?=.*\d).{8,}$/i,
  PHONE: /^\d{11,15}$/, // 11-15 digits, execute country code
  EMAIL:
    /^[\w+-]([\w+-]|[.-](?![.-]))*[\w+-]@([a-z0-9](-?[a-z0-9]+)*\.)+[a-z]{2,}$/i,
  ALPHABET_JP_REGX:
    /^[A-Z\x20\u3000\u3040-\u309F\u30A0-\u30FF\uFF65-\uFF9F\u4E00-\u9FAF\u3400-\u4DBF\u3005\uFA11]+$/i,
  WHITESPACE: /\s/,
  NON_WHITESPACE: /^\S*$/,
  KATAKANA: /^[\x20\u3000\u30A0-\u30FF\uFF65-\uFF9F]+$/,
  ZIP_CODE: /\d{7}/,
};

// Restaurant page constants
export const RESTAURANT = {
  ITEMS_PER_PAGE: 10,
  PAGE_TITLE: 'Discover Japan\'s Top Fine Dining',
  NO_RESULTS_TITLE: 'No restaurants found',
  NO_RESULTS_MESSAGE: 'Try adjusting your search criteria to find more results',
  ERROR_TITLE: 'Error loading restaurants',
  ERROR_MESSAGE: 'Please try again later',
};

export enum DateFormat {
  YEAR = 'YYYY',
  MONTH_YEAR_SHORT = 'MM/YY',
  YEAR_MONTH_DATE = 'YYYY/MM/DD',
  YEAR_MONTH = 'YYYY/MM',
  YEAR_MONTH_DASH = 'YYYY-MM',
  YEAR_MONTH_DAY_DASH = 'YYYY-MM-DD',
  YEAR_MONTH_DATE_HOUR = 'YYYY/MM/DD HH:mm',
  YEAR_MONTH_DATE_HOUR_DASH = 'YYYY-MM-DD HH:mm',
  HOUR_YEAR_MONTH_DATE = 'HH:mm YYYY-MM-DD',
  MONTH_DATE_YEAR = 'MM/DD/YYYY',
  // HOUR_YEAR_MONTH_DATE_JP = 'HH:mm YYYY年MM月DD日',
  // YEAR_MONTH_DATE_HOUR_JP = 'YYYY年MM月DD日 HH:mm',
  // YEAR_MONTH_DATE_JP = 'YYYY年MM月DD日',
  YEAR_MONTH_DATE_HOUR_MS = 'YYYY/MM/DD HH:mm:ss',
  // MONTH_DATE_HOUR_JP = 'YYYY年MM月DD日 HH時mm分',
  ISO = 'YYYY-MM-DDTHH:mm:ss.sss[Z]',
  MONTH_DATE_YEAR_LONG = 'MMMM DD, YYYY',
}

export const ERROR_CODE = {
  // --------------------------------------------------------
  // This item has an invalid format.
  GIM01: 'Invalid format.',
  // This item is required.
  GIM02: 'This field is required.',
  // Invalid current password.
  GIM03: 'Current password is incorrect.',
  // Passwords are not match.
  GIM04: 'Passwords are not match.',
  // Whitespace not allowed (when input space)
  GIM05: 'Spaces cannot be used.',
  /**
    The password must have:
    - at least 8 chars
    - include at least 1 number and at least 1 alphabet
   */
  GIM06: 'The password must have at least 8 chars, include number and alphabet only',
  // You can only upload JPG/JPEG/PNG file.
  GIM07:
    'Invalid file format. Please upload images in JPG, JPEG, or PNG format.',
  // The image size must not be greater than {size}.
  GIM08: 'Image size must be within {size}.',
  // The current date is not allowable.
  GIM09: 'Please enter a valid birth date.',
  // Please enter the correct phone number.
  GIM10: 'Please enter a valid phone number.',
  // The new password cannot be the same as the current password.
  GIM11: 'The new password cannot be the same as the current password.',
  // Object does not found
  GTM02: 'Can\'t find the item.',
  // Cannot get page data
  GTM03: 'Something went wrong.',
  // No internet connection
  GTM04: 'No network connection.',
  // Email already exist
  GTM09: 'This information can\'t be used for the registration.',
  // Email does not exist
  GTM10: 'The email does not exist.',
  // Not set first password
  GTM11: 'No password has been set.',
  // Invalid login information
  GTM12: 'Login does not succeed. Please check if it is correct email address or password once again.',
  // Not verify email
  GTM13: 'Please verify the email to activate your account.',
  // Account is inactivated when logging in
  GTM14: 'Login does not succeed. Please check if it is correct email address or password once again.',
  // Invitation expired
  GTM16: 'The invitation token is invalid or expired.',
  // Don't tick the Terms and Policies checkbox when registering
  GTM19: 'You are required to agree to the terms.',
  // When user resets password but their account are inactive
  GTM20: 'Login does not succeed. Please check if it is correct email address or password once again.',
  // User try login when they are rejected by operator
  GTM21: 'Login does not succeed. Please check if it is correct email address or password once again.',
  // User click on the resend button when they have successfully verified email
  GTM23: 'Email address verification has been completed.',
  // User try login when their account status is Requested/Invited or Pending
  GTM24: 'Your account is waiting for verification. Please check the inbox or spam to verify email and complete the registration process.',
  // Common error text for some rare cases
  GTM25: 'Your request is not valid, please try again.',
  // User try reset when they are rejected by operator
  GTM26: 'You can not reset password because your account is rejected.',
  // Invalid upload image/video file
  GTM35: 'You can only upload JPG, JPEG, PNG, WEBP, SVG, MP4, MOV file.',
  // Invalid upload video type
  GTM36: 'You can only upload MP4, MOV, WEBM file.',
  // Invalid upload document type
  GTM37: 'You can only upload PDF, DOC, DOCX, XLSX, PPTX, PPT, CSV, TXT, XLS file.',
  // Email address is incorrect
  GTM41: 'Please enter a valid email address.',
  // The amount of credit cards is over limit
  GTM42: 'You can only register 5 credit cards. Please delete unneeded cards in order to register new one.',
  // Credit card has been involved in a transaction/booking
  GTM43: 'Credit card is being used so it can\'t be deleted.',
  // The user is blocked on the 5th failed attempt
  GTM44: 'Your credit card registration failed attempt limit has been reached. For security reason, you can\'t register anymore cards today.',
  // Edit failed
  GTM45: 'Edit failed',
  // Delete failed
  GTM46: 'Delete failed',
  // Update failed
  GTM49: 'Update failed',
  // Send verification email
  GTM50: 'You have resend email for this account, please try again after 5 minutes.',
  // Invalid start-end time
  GTM51: 'Start time must be smaller than End time.',
  // Date time range is overlapped
  GTM52: 'Date time range cannot be the same or overlapped.',
  // Input wrong type for number
  GTM54: 'Please only input integer.',
};

export const ERROR_KEYS = {
  VERIFICATION_TOKEN_NOT_FOUND: 'VERIFICATION_TOKEN_NOT_FOUND',
  VERIFICATION_ACCOUNT_HAS_BEEN_COMPLETED:
    'VERIFICATION_ACCOUNT_HAS_BEEN_COMPLETED',
  VERIFICATION_INVALID_TOKEN: 'VERIFICATION_INVALID_TOKEN',
  VERIFICATION_EXPIRED_TOKEN: 'VERIFICATION_EXPIRED_TOKEN',
};

export const COOKIE_USER_DATA_KEY = '__user-common-data';
export const COOKIE_TOKEN_KEY = '__user-common-token';

export const CUSTOMER_VERIFY_EMAIL = 'customer-verify-email';
export const GENDER = [
  {
    label: 'Male',
    value: 'MALE',
  },
  {
    label: 'Female',
    value: 'FEMALE',
  },
  {
    label: 'Other',
    value: 'OTHER',
  },
];

// Country flag emoji mapping
export const COUNTRY_FLAGS: Record<string, string> = {
  af: '🇦🇫',
  al: '🇦🇱',
  dz: '🇩🇿',
  as: '🇦🇸',
  ad: '🇦🇩',
  ao: '🇦🇴',
  ai: '🇦🇮',
  ag: '🇦🇬',
  ar: '🇦🇷',
  am: '🇦🇲',
  aw: '🇦🇼',
  au: '🇦🇺',
  at: '🇦🇹',
  az: '🇦🇿',
  bs: '🇧🇸',
  bh: '🇧🇭',
  bd: '🇧🇩',
  bb: '🇧🇧',
  by: '🇧🇾',
  be: '🇧🇪',
  bz: '🇧🇿',
  bj: '🇧🇯',
  bm: '🇧🇲',
  bt: '🇧🇹',
  bo: '🇧🇴',
  ba: '🇧🇦',
  bw: '🇧🇼',
  br: '🇧🇷',
  io: '🇮🇴',
  vg: '🇻🇬',
  bn: '🇧🇳',
  bg: '🇧🇬',
  bf: '🇧🇫',
  bi: '🇧🇮',
  kh: '🇰🇭',
  cm: '🇨🇲',
  ca: '🇨🇦',
  cv: '🇨🇻',
  bq: '🇧🇶',
  ky: '🇰🇾',
  cf: '🇨🇫',
  td: '🇹🇩',
  cl: '🇨🇱',
  cn: '🇨🇳',
  co: '🇨🇴',
  km: '🇰🇲',
  cd: '🇨🇩',
  cg: '🇨🇬',
  ck: '🇨🇰',
  cr: '🇨🇷',
  ci: '🇨🇮',
  hr: '🇭🇷',
  cu: '🇨🇺',
  cw: '🇨🇼',
  cy: '🇨🇾',
  cz: '🇨🇿',
  dk: '🇩🇰',
  dj: '🇩🇯',
  dm: '🇩🇲',
  do: '🇩🇴',
  ec: '🇪🇨',
  eg: '🇪🇬',
  sv: '🇸🇻',
  gq: '🇬🇶',
  er: '🇪🇷',
  ee: '🇪🇪',
  et: '🇪🇹',
  fk: '🇫🇰',
  fo: '🇫🇴',
  fj: '🇫🇯',
  fi: '🇫🇮',
  fr: '🇫🇷',
  gf: '🇬🇫',
  pf: '🇵🇫',
  ga: '🇬🇦',
  gm: '🇬🇲',
  ge: '🇬🇪',
  de: '🇩🇪',
  gh: '🇬🇭',
  gi: '🇬🇮',
  gr: '🇬🇷',
  gl: '🇬🇱',
  gd: '🇬🇩',
  gp: '🇬🇵',
  gu: '🇬🇺',
  gt: '🇬🇹',
  gn: '🇬🇳',
  gw: '🇬🇼',
  gy: '🇬🇾',
  ht: '🇭🇹',
  hn: '🇭🇳',
  hk: '🇭🇰',
  hu: '🇭🇺',
  is: '🇮🇸',
  in: '🇮🇳',
  id: '🇮🇩',
  ir: '🇮🇷',
  iq: '🇮🇶',
  ie: '🇮🇪',
  il: '🇮🇱',
  it: '🇮🇹',
  jm: '🇯🇲',
  jp: '🇯🇵',
  jo: '🇯🇴',
  kz: '🇰🇿',
  ke: '🇰🇪',
  ki: '🇰🇮',
  ks: '🇽🇰',
  kw: '🇰🇼',
  kg: '🇰🇬',
  la: '🇱🇦',
  lv: '🇱🇻',
  lb: '🇱🇧',
  ls: '🇱🇸',
  lr: '🇱🇷',
  ly: '🇱🇾',
  li: '🇱🇮',
  lt: '🇱🇹',
  lu: '🇱🇺',
  mo: '🇲🇴',
  mk: '🇲🇰',
  mg: '🇲🇬',
  mw: '🇲🇼',
  my: '🇲🇾',
  mv: '🇲🇻',
  ml: '🇲🇱',
  mt: '🇲🇹',
  mh: '🇲🇭',
  mq: '🇲🇶',
  mr: '🇲🇷',
  mu: '🇲🇺',
  mx: '🇲🇽',
  fm: '🇫🇲',
  md: '🇲🇩',
  mc: '🇲🇨',
  mn: '🇲🇳',
  me: '🇲🇪',
  ms: '🇲🇸',
  ma: '🇲🇦',
  mz: '🇲🇿',
  mm: '🇲🇲',
  na: '🇳🇦',
  nr: '🇳🇷',
  np: '🇳🇵',
  nl: '🇳🇱',
  nc: '🇳🇨',
  nz: '🇳🇿',
  ni: '🇳🇮',
  ne: '🇳🇪',
  ng: '🇳🇬',
  nu: '🇳🇺',
  nf: '🇳🇫',
  kp: '🇰🇵',
  mp: '🇲🇵',
  no: '🇳🇴',
  om: '🇴🇲',
  pk: '🇵🇰',
  pw: '🇵🇼',
  ps: '🇵🇸',
  pa: '🇵🇦',
  pg: '🇵🇬',
  py: '🇵🇾',
  pe: '🇵🇪',
  ph: '🇵🇭',
  pl: '🇵🇱',
  pt: '🇵🇹',
  pr: '🇵🇷',
  qa: '🇶🇦',
  re: '🇷🇪',
  ro: '🇷🇴',
  ru: '🇷🇺',
  rw: '🇷🇼',
  bl: '🇧🇱',
  sh: '🇸🇭',
  kn: '🇰🇳',
  lc: '🇱🇨',
  mf: '🇲🇫',
  pm: '🇵🇲',
  vc: '🇻🇨',
  ws: '🇼🇸',
  sm: '🇸🇲',
  st: '🇸🇹',
  sa: '🇸🇦',
  sn: '🇸🇳',
  rs: '🇷🇸',
  sc: '🇸🇨',
  sl: '🇸🇱',
  sg: '🇸🇬',
  sx: '🇸🇽',
  sk: '🇸🇰',
  si: '🇸🇮',
  sb: '🇸🇧',
  so: '🇸🇴',
  za: '🇿🇦',
  kr: '🇰🇷',
  ss: '🇸🇸',
  es: '🇪🇸',
  lk: '🇱🇰',
  sd: '🇸🇩',
  sr: '🇸🇷',
  sz: '🇸🇿',
  se: '🇸🇪',
  ch: '🇨🇭',
  sy: '🇸🇾',
  tw: '🇹🇼',
  tj: '🇹🇯',
  tz: '🇹🇿',
  th: '🇹🇭',
  tl: '🇹🇱',
  tg: '🇹🇬',
  tk: '🇹🇰',
  to: '🇹🇴',
  tt: '🇹🇹',
  tn: '🇹🇳',
  tr: '🇹🇷',
  tm: '🇹🇲',
  tc: '🇹🇨',
  tv: '🇹🇻',
  vi: '🇻🇮',
  ug: '🇺🇬',
  ua: '🇺🇦',
  ae: '🇦🇪',
  gb: '🇬🇧',
  us: '🇺🇸',
  uy: '🇺🇾',
  uz: '🇺🇿',
  vu: '🇻🇺',
  va: '🇻🇦',
  ve: '🇻🇪',
  vn: '🇻🇳',
  wf: '🇼🇫',
  ye: '🇾🇪',
  zm: '🇿🇲',
  zw: '🇿🇼',
};

// Raw country data for phone numbers: [name, regions, countryCode, dialCode]
export const RAW_ALL_COUNTRIES: Array<[string, string[], string, string]> = [
  ['Afghanistan', ['asia'], 'af', '93'],
  ['Albania', ['europe'], 'al', '355'],
  ['Algeria', ['africa', 'north-africa'], 'dz', '213'],
  ['American Samoa', ['oceania'], 'as', '1684'],
  ['Andorra', ['europe'], 'ad', '376'],
  ['Angola', ['africa'], 'ao', '244'],
  ['Anguilla', ['america', 'carribean'], 'ai', '1264'],
  ['Antigua and Barbuda', ['america', 'carribean'], 'ag', '1268'],
  ['Argentina', ['america', 'south-america'], 'ar', '54'],
  ['Armenia', ['asia', 'ex-ussr'], 'am', '374'],
  ['Aruba', ['america', 'carribean'], 'aw', '297'],
  ['Australia', ['oceania'], 'au', '61'],
  ['Austria', ['europe', 'european-union'], 'at', '43'],
  ['Azerbaijan', ['asia', 'ex-ussr'], 'az', '994'],
  ['Bahamas', ['america', 'carribean'], 'bs', '1242'],
  ['Bahrain', ['middle-east'], 'bh', '973'],
  ['Bangladesh', ['asia'], 'bd', '880'],
  ['Barbados', ['america', 'carribean'], 'bb', '1246'],
  ['Belarus', ['europe', 'ex-ussr'], 'by', '375'],
  ['Belgium', ['europe', 'european-union'], 'be', '32'],
  ['Belize', ['america', 'central-america'], 'bz', '501'],
  ['Benin', ['africa'], 'bj', '229'],
  ['Bermuda', ['america', 'north-america'], 'bm', '1441'],
  ['Bhutan', ['asia'], 'bt', '975'],
  ['Bolivia', ['america', 'south-america'], 'bo', '591'],
  ['Bosnia and Herzegovina', ['europe'], 'ba', '387'],
  ['Botswana', ['africa'], 'bw', '267'],
  ['Brazil', ['america', 'south-america'], 'br', '55'],
  ['British Indian Ocean Territory', ['asia'], 'io', '246'],
  ['British Virgin Islands', ['america', 'carribean'], 'vg', '1284'],
  ['Brunei', ['asia'], 'bn', '673'],
  ['Bulgaria', ['europe', 'european-union'], 'bg', '359'],
  ['Burkina Faso', ['africa'], 'bf', '226'],
  ['Burundi', ['africa'], 'bi', '257'],
  ['Cambodia', ['asia'], 'kh', '855'],
  ['Cameroon', ['africa'], 'cm', '237'],
  ['Canada', ['america', 'north-america'], 'ca', '1'],
  ['Cape Verde', ['africa'], 'cv', '238'],
  ['Caribbean Netherlands', ['america', 'carribean'], 'bq', '599'],
  ['Cayman Islands', ['america', 'carribean'], 'ky', '1345'],
  ['Central African Republic', ['africa'], 'cf', '236'],
  ['Chad', ['africa'], 'td', '235'],
  ['Chile', ['america', 'south-america'], 'cl', '56'],
  ['China', ['asia'], 'cn', '86'],
  ['Colombia', ['america', 'south-america'], 'co', '57'],
  ['Comoros', ['africa'], 'km', '269'],
  ['Congo', ['africa'], 'cd', '243'],
  ['Congo', ['africa'], 'cg', '242'],
  ['Cook Islands', ['oceania'], 'ck', '682'],
  ['Costa Rica', ['america', 'central-america'], 'cr', '506'],
  ['Côte d\'Ivoire', ['africa'], 'ci', '225'],
  ['Croatia', ['europe', 'european-union'], 'hr', '385'],
  ['Cuba', ['america', 'carribean'], 'cu', '53'],
  ['Curaçao', ['america', 'carribean'], 'cw', '599'],
  ['Cyprus', ['europe', 'european-union'], 'cy', '357'],
  ['Czech Republic', ['europe', 'european-union'], 'cz', '420'],
  ['Denmark', ['europe', 'european-union'], 'dk', '45'],
  ['Djibouti', ['africa'], 'dj', '253'],
  ['Dominica', ['america', 'carribean'], 'dm', '1767'],
  ['Dominican Republic', ['america', 'carribean'], 'do', '1'],
  ['Ecuador', ['america', 'south-america'], 'ec', '593'],
  ['Egypt', ['africa', 'north-africa'], 'eg', '20'],
  ['El Salvador', ['america', 'central-america'], 'sv', '503'],
  ['Equatorial Guinea', ['africa'], 'gq', '240'],
  ['Eritrea', ['africa'], 'er', '291'],
  ['Estonia', ['europe', 'european-union', 'ex-ussr'], 'ee', '372'],
  ['Ethiopia', ['africa'], 'et', '251'],
  ['Falkland Islands', ['america', 'south-america'], 'fk', '500'],
  ['Faroe Islands', ['europe'], 'fo', '298'],
  ['Fiji', ['oceania'], 'fj', '679'],
  ['Finland', ['europe', 'european-union'], 'fi', '358'],
  ['France', ['europe', 'european-union'], 'fr', '33'],
  ['French Guiana', ['america', 'south-america'], 'gf', '594'],
  ['French Polynesia', ['oceania'], 'pf', '689'],
  ['Gabon', ['africa'], 'ga', '241'],
  ['Gambia', ['africa'], 'gm', '220'],
  ['Georgia', ['asia', 'ex-ussr'], 'ge', '995'],
  ['Germany', ['europe', 'european-union'], 'de', '49'],
  ['Ghana', ['africa'], 'gh', '233'],
  ['Gibraltar', ['europe'], 'gi', '350'],
  ['Greece', ['europe', 'european-union'], 'gr', '30'],
  ['Greenland', ['america'], 'gl', '299'],
  ['Grenada', ['america', 'carribean'], 'gd', '1473'],
  ['Guadeloupe', ['america', 'carribean'], 'gp', '590'],
  ['Guam', ['oceania'], 'gu', '1671'],
  ['Guatemala', ['america', 'central-america'], 'gt', '502'],
  ['Guinea', ['africa'], 'gn', '224'],
  ['Guinea-Bissau', ['africa'], 'gw', '245'],
  ['Guyana', ['america', 'south-america'], 'gy', '592'],
  ['Haiti', ['america', 'carribean'], 'ht', '509'],
  ['Honduras', ['america', 'central-america'], 'hn', '504'],
  ['Hong Kong', ['asia'], 'hk', '852'],
  ['Hungary', ['europe', 'european-union'], 'hu', '36'],
  ['Iceland', ['europe'], 'is', '354'],
  ['India', ['asia'], 'in', '91'],
  ['Indonesia', ['asia'], 'id', '62'],
  ['Iran', ['middle-east'], 'ir', '98'],
  ['Iraq', ['middle-east'], 'iq', '964'],
  ['Ireland', ['europe', 'european-union'], 'ie', '353'],
  ['Israel', ['middle-east'], 'il', '972'],
  ['Italy', ['europe', 'european-union'], 'it', '39'],
  ['Jamaica', ['america', 'carribean'], 'jm', '1876'],
  ['Japan', ['asia'], 'jp', '81'],
  ['Jordan', ['middle-east'], 'jo', '962'],
  ['Kazakhstan', ['asia', 'ex-ussr'], 'kz', '7'],
  ['Kenya', ['africa'], 'ke', '254'],
  ['Kiribati', ['oceania'], 'ki', '686'],
  ['Kosovo', ['europe'], 'ks', '383'],
  ['Kuwait', ['middle-east'], 'kw', '965'],
  ['Kyrgyzstan', ['asia', 'ex-ussr'], 'kg', '996'],
  ['Laos', ['asia'], 'la', '856'],
  ['Latvia', ['europe', 'european-union', 'ex-ussr'], 'lv', '371'],
  ['Lebanon', ['middle-east'], 'lb', '961'],
  ['Lesotho', ['africa'], 'ls', '266'],
  ['Liberia', ['africa'], 'lr', '231'],
  ['Libya', ['africa', 'north-africa'], 'ly', '218'],
  ['Liechtenstein', ['europe'], 'li', '423'],
  ['Lithuania', ['europe', 'european-union', 'ex-ussr'], 'lt', '370'],
  ['Luxembourg', ['europe', 'european-union'], 'lu', '352'],
  ['Macau', ['asia'], 'mo', '853'],
  ['Macedonia', ['europe'], 'mk', '389'],
  ['Madagascar', ['africa'], 'mg', '261'],
  ['Malawi', ['africa'], 'mw', '265'],
  ['Malaysia', ['asia'], 'my', '60'],
  ['Maldives', ['asia'], 'mv', '960'],
  ['Mali', ['africa'], 'ml', '223'],
  ['Malta', ['europe', 'european-union'], 'mt', '356'],
  ['Marshall Islands', ['oceania'], 'mh', '692'],
  ['Martinique', ['america', 'carribean'], 'mq', '596'],
  ['Mauritania', ['africa'], 'mr', '222'],
  ['Mauritius', ['africa'], 'mu', '230'],
  ['Mexico', ['america', 'central-america'], 'mx', '52'],
  ['Micronesia', ['oceania'], 'fm', '691'],
  ['Moldova', ['europe'], 'md', '373'],
  ['Monaco', ['europe'], 'mc', '377'],
  ['Mongolia', ['asia'], 'mn', '976'],
  ['Montenegro', ['europe'], 'me', '382'],
  ['Montserrat', ['america', 'carribean'], 'ms', '1664'],
  ['Morocco', ['africa', 'north-africa'], 'ma', '212'],
  ['Mozambique', ['africa'], 'mz', '258'],
  ['Myanmar', ['asia'], 'mm', '95'],
  ['Namibia', ['africa'], 'na', '264'],
  ['Nauru', ['africa'], 'nr', '674'],
  ['Nepal', ['asia'], 'np', '977'],
  ['Netherlands', ['europe', 'european-union'], 'nl', '31'],
  ['New Caledonia', ['oceania'], 'nc', '687'],
  ['New Zealand', ['oceania'], 'nz', '64'],
  ['Nicaragua', ['america', 'central-america'], 'ni', '505'],
  ['Niger', ['africa'], 'ne', '227'],
  ['Nigeria', ['africa'], 'ng', '234'],
  ['Niue', ['asia'], 'nu', '683'],
  ['Norfolk Island', ['oceania'], 'nf', '672'],
  ['North Korea', ['asia'], 'kp', '850'],
  ['Northern Mariana Islands', ['oceania'], 'mp', '1670'],
  ['Norway', ['europe'], 'no', '47'],
  ['Oman', ['middle-east'], 'om', '968'],
  ['Pakistan', ['asia'], 'pk', '92'],
  ['Palau', ['oceania'], 'pw', '680'],
  ['Palestine', ['middle-east'], 'ps', '970'],
  ['Panama', ['america', 'central-america'], 'pa', '507'],
  ['Papua New Guinea', ['oceania'], 'pg', '675'],
  ['Paraguay', ['america', 'south-america'], 'py', '595'],
  ['Peru', ['america', 'south-america'], 'pe', '51'],
  ['Philippines', ['asia'], 'ph', '63'],
  ['Poland', ['europe', 'european-union'], 'pl', '48'],
  ['Portugal', ['europe', 'european-union'], 'pt', '351'],
  ['Puerto Rico', ['america', 'carribean'], 'pr', '1'],
  ['Qatar', ['middle-east'], 'qa', '974'],
  ['Réunion', ['africa'], 're', '262'],
  ['Romania', ['europe', 'european-union'], 'ro', '40'],
  ['Russia', ['europe', 'asia', 'ex-ussr'], 'ru', '7'],
  ['Rwanda', ['africa'], 'rw', '250'],
  ['Saint Barthélemy', ['america', 'carribean'], 'bl', '590'],
  ['Saint Helena', ['africa'], 'sh', '290'],
  ['Saint Kitts and Nevis', ['america', 'carribean'], 'kn', '1869'],
  ['Saint Lucia', ['america', 'carribean'], 'lc', '1758'],
  ['Saint Martin', ['america', 'carribean'], 'mf', '590'],
  ['Saint Pierre and Miquelon', ['america', 'north-america'], 'pm', '508'],
  ['Saint Vincent and the Grenadines', ['america', 'carribean'], 'vc', '1784'],
  ['Samoa', ['oceania'], 'ws', '685'],
  ['San Marino', ['europe'], 'sm', '378'],
  ['São Tomé and Príncipe', ['africa'], 'st', '239'],
  ['Saudi Arabia', ['middle-east'], 'sa', '966'],
  ['Senegal', ['africa'], 'sn', '221'],
  ['Serbia', ['europe'], 'rs', '381'],
  ['Seychelles', ['africa'], 'sc', '248'],
  ['Sierra Leone', ['africa'], 'sl', '232'],
  ['Singapore', ['asia'], 'sg', '65'],
  ['Sint Maarten', ['america', 'carribean'], 'sx', '1721'],
  ['Slovakia', ['europe', 'european-union'], 'sk', '421'],
  ['Slovenia', ['europe', 'european-union'], 'si', '386'],
  ['Solomon Islands', ['oceania'], 'sb', '677'],
  ['Somalia', ['africa'], 'so', '252'],
  ['South Africa', ['africa'], 'za', '27'],
  ['South Korea', ['asia'], 'kr', '82'],
  ['South Sudan', ['africa', 'north-africa'], 'ss', '211'],
  ['Spain', ['europe', 'european-union'], 'es', '34'],
  ['Sri Lanka', ['asia'], 'lk', '94'],
  ['Sudan', ['africa'], 'sd', '249'],
  ['Suriname', ['america', 'south-america'], 'sr', '597'],
  ['Swaziland', ['africa'], 'sz', '268'],
  ['Sweden', ['europe', 'european-union'], 'se', '46'],
  ['Switzerland', ['europe'], 'ch', '41'],
  ['Syria', ['middle-east'], 'sy', '963'],
  ['Taiwan', ['asia'], 'tw', '886'],
  ['Tajikistan', ['asia', 'ex-ussr'], 'tj', '992'],
  ['Tanzania', ['africa'], 'tz', '255'],
  ['Thailand', ['asia'], 'th', '66'],
  ['Timor-Leste', ['asia'], 'tl', '670'],
  ['Togo', ['africa'], 'tg', '228'],
  ['Tokelau', ['oceania'], 'tk', '690'],
  ['Tonga', ['oceania'], 'to', '676'],
  ['Trinidad and Tobago', ['america', 'carribean'], 'tt', '1868'],
  ['Tunisia', ['africa', 'north-africa'], 'tn', '216'],
  ['Turkey', ['europe'], 'tr', '90'],
  ['Turkmenistan', ['asia', 'ex-ussr'], 'tm', '993'],
  ['Turks and Caicos Islands', ['america', 'carribean'], 'tc', '1649'],
  ['Tuvalu', ['asia'], 'tv', '688'],
  ['U.S. Virgin Islands', ['america', 'carribean'], 'vi', '1340'],
  ['Uganda', ['africa'], 'ug', '256'],
  ['Ukraine', ['europe', 'ex-ussr'], 'ua', '380'],
  ['United Arab Emirates', ['middle-east'], 'ae', '971'],
  ['United Kingdom', ['europe', 'european-union'], 'gb', '44'],
  ['United States', ['america', 'north-america'], 'us', '1'],
  ['Uruguay', ['america', 'south-america'], 'uy', '598'],
  ['Uzbekistan', ['asia', 'ex-ussr'], 'uz', '998'],
  ['Vanuatu', ['oceania'], 'vu', '678'],
  ['Vatican City', ['europe'], 'va', '39'],
  ['Venezuela', ['america', 'south-america'], 've', '58'],
  ['Vietnam', ['asia'], 'vn', '84'],
  ['Wallis and Futuna', ['oceania'], 'wf', '681'],
  ['Yemen', ['middle-east'], 'ye', '967'],
  ['Zambia', ['africa'], 'zm', '260'],
  ['Zimbabwe', ['africa'], 'zw', '263'],
];

// Popular countries for prioritization in dropdown
export const POPULAR_COUNTRIES = ['jp', 'us', 'gb', 'cn', 'kr', 'fr', 'de', 'in', 'au', 'br', 'ca', 'it', 'mx', 'es', 'nl', 'ru'];

// Mapping of dial codes to country codes for phone number formatting - Generated from RAW_ALL_COUNTRIES
// Note: Some dial codes are shared by multiple countries, we use the primary/most common one
export const DIAL_CODE_TO_COUNTRY: Record<string, string> = {
  '+93': 'AF', // Afghanistan
  '+355': 'AL', // Albania
  '+213': 'DZ', // Algeria
  '+1684': 'AS', // American Samoa
  '+376': 'AD', // Andorra
  '+244': 'AO', // Angola
  '+1264': 'AI', // Anguilla
  '+1268': 'AG', // Antigua and Barbuda
  '+54': 'AR', // Argentina
  '+374': 'AM', // Armenia
  '+297': 'AW', // Aruba
  '+61': 'AU', // Australia
  '+43': 'AT', // Austria
  '+994': 'AZ', // Azerbaijan
  '+1242': 'BS', // Bahamas
  '+973': 'BH', // Bahrain
  '+880': 'BD', // Bangladesh
  '+1246': 'BB', // Barbados
  '+375': 'BY', // Belarus
  '+32': 'BE', // Belgium
  '+501': 'BZ', // Belize
  '+229': 'BJ', // Benin
  '+1441': 'BM', // Bermuda
  '+975': 'BT', // Bhutan
  '+591': 'BO', // Bolivia
  '+387': 'BA', // Bosnia and Herzegovina
  '+267': 'BW', // Botswana
  '+55': 'BR', // Brazil
  '+246': 'IO', // British Indian Ocean Territory
  '+1284': 'VG', // British Virgin Islands
  '+673': 'BN', // Brunei
  '+359': 'BG', // Bulgaria
  '+226': 'BF', // Burkina Faso
  '+257': 'BI', // Burundi
  '+855': 'KH', // Cambodia
  '+237': 'CM', // Cameroon
  '+1': 'US', // United States (primary for +1)
  '+238': 'CV', // Cape Verde
  '+599': 'BQ', // Caribbean Netherlands (primary for +599)
  '+1345': 'KY', // Cayman Islands
  '+236': 'CF', // Central African Republic
  '+235': 'TD', // Chad
  '+56': 'CL', // Chile
  '+86': 'CN', // China
  '+57': 'CO', // Colombia
  '+269': 'KM', // Comoros
  '+243': 'CD', // Congo (Democratic Republic)
  '+242': 'CG', // Congo (Republic)
  '+682': 'CK', // Cook Islands
  '+506': 'CR', // Costa Rica
  '+225': 'CI', // Côte d'Ivoire
  '+385': 'HR', // Croatia
  '+53': 'CU', // Cuba
  '+357': 'CY', // Cyprus
  '+420': 'CZ', // Czech Republic
  '+45': 'DK', // Denmark
  '+253': 'DJ', // Djibouti
  '+1767': 'DM', // Dominica
  '+593': 'EC', // Ecuador
  '+20': 'EG', // Egypt
  '+503': 'SV', // El Salvador
  '+240': 'GQ', // Equatorial Guinea
  '+291': 'ER', // Eritrea
  '+372': 'EE', // Estonia
  '+251': 'ET', // Ethiopia
  '+500': 'FK', // Falkland Islands
  '+298': 'FO', // Faroe Islands
  '+679': 'FJ', // Fiji
  '+358': 'FI', // Finland
  '+33': 'FR', // France
  '+594': 'GF', // French Guiana
  '+689': 'PF', // French Polynesia
  '+241': 'GA', // Gabon
  '+220': 'GM', // Gambia
  '+995': 'GE', // Georgia
  '+49': 'DE', // Germany
  '+233': 'GH', // Ghana
  '+350': 'GI', // Gibraltar
  '+30': 'GR', // Greece
  '+299': 'GL', // Greenland
  '+1473': 'GD', // Grenada
  '+590': 'GP', // Guadeloupe (primary for +590)
  '+1671': 'GU', // Guam
  '+502': 'GT', // Guatemala
  '+224': 'GN', // Guinea
  '+245': 'GW', // Guinea-Bissau
  '+592': 'GY', // Guyana
  '+509': 'HT', // Haiti
  '+504': 'HN', // Honduras
  '+852': 'HK', // Hong Kong
  '+36': 'HU', // Hungary
  '+354': 'IS', // Iceland
  '+91': 'IN', // India
  '+62': 'ID', // Indonesia
  '+98': 'IR', // Iran
  '+964': 'IQ', // Iraq
  '+353': 'IE', // Ireland
  '+972': 'IL', // Israel
  '+39': 'IT', // Italy (primary for +39)
  '+1876': 'JM', // Jamaica
  '+81': 'JP', // Japan
  '+962': 'JO', // Jordan
  '+7': 'RU', // Russia (primary for +7, shared with Kazakhstan)
  '+254': 'KE', // Kenya
  '+686': 'KI', // Kiribati
  '+383': 'KS', // Kosovo
  '+965': 'KW', // Kuwait
  '+996': 'KG', // Kyrgyzstan
  '+856': 'LA', // Laos
  '+371': 'LV', // Latvia
  '+961': 'LB', // Lebanon
  '+266': 'LS', // Lesotho
  '+231': 'LR', // Liberia
  '+218': 'LY', // Libya
  '+423': 'LI', // Liechtenstein
  '+370': 'LT', // Lithuania
  '+352': 'LU', // Luxembourg
  '+853': 'MO', // Macau
  '+389': 'MK', // Macedonia
  '+261': 'MG', // Madagascar
  '+265': 'MW', // Malawi
  '+60': 'MY', // Malaysia
  '+960': 'MV', // Maldives
  '+223': 'ML', // Mali
  '+356': 'MT', // Malta
  '+692': 'MH', // Marshall Islands
  '+596': 'MQ', // Martinique
  '+222': 'MR', // Mauritania
  '+230': 'MU', // Mauritius
  '+52': 'MX', // Mexico
  '+691': 'FM', // Micronesia
  '+373': 'MD', // Moldova
  '+377': 'MC', // Monaco
  '+976': 'MN', // Mongolia
  '+382': 'ME', // Montenegro
  '+1664': 'MS', // Montserrat
  '+212': 'MA', // Morocco
  '+258': 'MZ', // Mozambique
  '+95': 'MM', // Myanmar
  '+264': 'NA', // Namibia
  '+674': 'NR', // Nauru
  '+977': 'NP', // Nepal
  '+31': 'NL', // Netherlands
  '+687': 'NC', // New Caledonia
  '+64': 'NZ', // New Zealand
  '+505': 'NI', // Nicaragua
  '+227': 'NE', // Niger
  '+234': 'NG', // Nigeria
  '+683': 'NU', // Niue
  '+672': 'NF', // Norfolk Island
  '+850': 'KP', // North Korea
  '+1670': 'MP', // Northern Mariana Islands
  '+47': 'NO', // Norway
  '+968': 'OM', // Oman
  '+92': 'PK', // Pakistan
  '+680': 'PW', // Palau
  '+970': 'PS', // Palestine
  '+507': 'PA', // Panama
  '+675': 'PG', // Papua New Guinea
  '+595': 'PY', // Paraguay
  '+51': 'PE', // Peru
  '+63': 'PH', // Philippines
  '+48': 'PL', // Poland
  '+351': 'PT', // Portugal
  '+974': 'QA', // Qatar
  '+262': 'RE', // Réunion
  '+40': 'RO', // Romania
  '+250': 'RW', // Rwanda
  '+290': 'SH', // Saint Helena
  '+1869': 'KN', // Saint Kitts and Nevis
  '+1758': 'LC', // Saint Lucia
  '+508': 'PM', // Saint Pierre and Miquelon
  '+1784': 'VC', // Saint Vincent and the Grenadines
  '+685': 'WS', // Samoa
  '+378': 'SM', // San Marino
  '+239': 'ST', // São Tomé and Príncipe
  '+966': 'SA', // Saudi Arabia
  '+221': 'SN', // Senegal
  '+381': 'RS', // Serbia
  '+248': 'SC', // Seychelles
  '+232': 'SL', // Sierra Leone
  '+65': 'SG', // Singapore
  '+1721': 'SX', // Sint Maarten
  '+421': 'SK', // Slovakia
  '+386': 'SI', // Slovenia
  '+677': 'SB', // Solomon Islands
  '+252': 'SO', // Somalia
  '+27': 'ZA', // South Africa
  '+82': 'KR', // South Korea
  '+211': 'SS', // South Sudan
  '+34': 'ES', // Spain
  '+94': 'LK', // Sri Lanka
  '+249': 'SD', // Sudan
  '+597': 'SR', // Suriname
  '+268': 'SZ', // Swaziland
  '+46': 'SE', // Sweden
  '+41': 'CH', // Switzerland
  '+963': 'SY', // Syria
  '+886': 'TW', // Taiwan
  '+992': 'TJ', // Tajikistan
  '+255': 'TZ', // Tanzania
  '+66': 'TH', // Thailand
  '+670': 'TL', // Timor-Leste
  '+228': 'TG', // Togo
  '+690': 'TK', // Tokelau
  '+676': 'TO', // Tonga
  '+1868': 'TT', // Trinidad and Tobago
  '+216': 'TN', // Tunisia
  '+90': 'TR', // Turkey
  '+993': 'TM', // Turkmenistan
  '+1649': 'TC', // Turks and Caicos Islands
  '+688': 'TV', // Tuvalu
  '+1340': 'VI', // U.S. Virgin Islands
  '+256': 'UG', // Uganda
  '+380': 'UA', // Ukraine
  '+971': 'AE', // United Arab Emirates
  '+44': 'GB', // United Kingdom
  '+598': 'UY', // Uruguay
  '+998': 'UZ', // Uzbekistan
  '+678': 'VU', // Vanuatu
  '+58': 'VE', // Venezuela
  '+84': 'VN', // Vietnam
  '+681': 'WF', // Wallis and Futuna
  '+967': 'YE', // Yemen
  '+260': 'ZM', // Zambia
  '+263': 'ZW', // Zimbabwe
};

// Legacy COUNTRIES array for backward compatibility - static JSON format
export const COUNTRIES = [
  { label: 'South Korea', value: 'KR', code: '+82', flag: '🇰🇷' },
  { label: 'China', value: 'CN', code: '+86', flag: '🇨🇳' },
  { label: 'Taiwan', value: 'TW', code: '+886', flag: '🇹🇼' },
  { label: 'Hong Kong', value: 'HK', code: '+852', flag: '🇭🇰' },
  { label: 'Thailand', value: 'TH', code: '+66', flag: '🇹🇭' },
  { label: 'Singapore', value: 'SG', code: '+65', flag: '🇸🇬' },
  { label: 'Malaysia', value: 'MY', code: '+60', flag: '🇲🇾' },
  { label: 'Indonesia', value: 'ID', code: '+62', flag: '🇮🇩' },
  { label: 'Philippines', value: 'PH', code: '+63', flag: '🇵🇭' },
  { label: 'Viet Nam', value: 'VN', code: '+84', flag: '🇻🇳' },
  { label: 'India', value: 'IN', code: '+91', flag: '🇮🇳' },
  { label: 'Australia', value: 'AU', code: '+61', flag: '🇦🇺' },
  { label: 'U.S.A.', value: 'US', code: '+1', flag: '🇺🇸' },
  { label: 'Canada', value: 'CA', code: '+1', flag: '🇨🇦' },
  { label: 'Mexico', value: 'MX', code: '+52', flag: '🇲🇽' },
  { label: 'United Kingdom', value: 'GB', code: '+44', flag: '🇬🇧' },
  { label: 'France', value: 'FR', code: '+33', flag: '🇫🇷' },
  { label: 'Germany', value: 'DE', code: '+49', flag: '🇩🇪' },
  { label: 'Italy', value: 'IT', code: '+39', flag: '🇮🇹' },
  { label: 'Spain', value: 'ES', code: '+34', flag: '🇪🇸' },
  { label: 'Russia', value: 'RU', code: '+7', flag: '🇷🇺' },
  { label: 'Others', value: 'XX', code: '+000', flag: '🏳️' },
];
