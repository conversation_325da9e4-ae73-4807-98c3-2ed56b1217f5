.wrapper {
  --input-height: 48px;
}

.input {
  /* 12px + 60px + 8px */
  --input-padding-inline-end: 80px;
  &:focus,
  &:focus-within:not([data-error]) {
    box-shadow: 0px 0px 0px 3px rgba(255, 178, 52, 0.4);
  }
}

.innerInput {
  // Apply password masking styles only when password is hidden
  &[type='password'] {
    &:not(:placeholder-shown) {
      transform: translateY(-2px);
      font-family: 'Verdana', sans-serif;
      font-size: 28px;
      letter-spacing: 3px;
      line-height: 1.2;
    }
    
    &::placeholder {
      font-size: 14px;
      font-family: inherit;
      transform: none;
      letter-spacing: normal;
    }
    
    // Handle autofill for masked passwords
    &:-webkit-autofill {
      -webkit-box-shadow: 0 0 0 30px white inset !important;
      -webkit-text-fill-color: #333 !important;
      -webkit-text-security: disc;
    }
    &:-webkit-autofill:hover
    &:-webkit-autofill:focus, 
    &:-webkit-autofill:active {
      transform: translateY(-2px);
      -webkit-box-shadow: 0 0 0 30px white inset !important;
      -webkit-text-fill-color: #333 !important;
      -webkit-text-security: disc;
      font-family: 'Verdana', sans-serif;
      font-size: 28px;
      letter-spacing: 3px;
    }
  }
  
  // When password is visible (text input)
  &[type='text'] {
    &:not(:placeholder-shown) {
      font-family: inherit;
      font-size: 14px;
      letter-spacing: normal;
      transform: none;
    }
    
    &:-webkit-autofill,
    &:-webkit-autofill:hover, 
    &:-webkit-autofill:focus, 
    &:-webkit-autofill:active {
      -webkit-box-shadow: 0 0 0 30px white inset !important;
      -webkit-text-fill-color: #333 !important;
      font-family: inherit;
      font-size: 14px;
      letter-spacing: normal;
      transform: none;
    }
  }
}

.section {
  justify-content: flex-end;
  &[data-position='right'] {
    --section-end: 12px;
  }
}

.visibilityToggle {
  width: auto;
  min-width: 44px; // Better touch target
  height: 32px;
  border-radius: 6px;
  transition: all 0.2s ease;
  background-color: transparent !important;
  
  // Disabled state
  &:disabled,
  &[data-disabled] {
    opacity: 0.5;
    cursor: not-allowed;
    background-color: transparent !important;
  }
  
  // Loading state
  &[data-loading] {
    opacity: 0.7;
    cursor: wait;
    background-color: transparent !important;
  }
}
