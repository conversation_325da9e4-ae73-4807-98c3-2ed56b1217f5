.input {
  margin: 0;
  --input-bd: var(--mantine-color-gray-5);
  --input-color: var(--mantine-color-gray-8);
  --input-bg: var(--mantine-color-gray-0);
  --input-bd-focus: var(--mantine-color-primary-6);
  --input-placeholder-color: var(--mantine-color-gray-4);
  --input-section-color: var(--mantine-color-gray-5);
  /* Disabled state */
  --input-disabled-bg: var(--mantine-color-gray-1);
  --input-disabled-color: var(--mantine-color-gray-5);
  transition: all 100ms linear;
  border-radius: 4px;

  &[data-error] {
    --input-bd: var(--mantine-color-error-3);
    --input-color: var(--mantine-color-gray-8);
    --input-placeholder-color: var(--mantine-color-gray-4);
    --input-section-color: var(--mantine-color-gray-5);
    &:focus,
    &:focus-within {
      --input-bd: var(--mantine-color-error-3);
    }
  }
  &:focus:not([data-error]) {
    box-shadow: 0px 0px 0px 3px rgba(255, 178, 52, 0.4);
  }
}

.label {
  color: #393F47;
  font-size: 14px;
  font-weight: 400;
  margin-bottom: 8px;
}

.section {
  margin-right: -4px;
}

.option {
  border-radius: 4px;
  padding: 17px 16px;
  font-size: 14px;
  line-height: 24px;
  font-weight: 400;
  
  &:hover, &:active, &:focus {
    background: var(--mantine-color-primary-0);
    color: var(--mantine-color-gray-8);
  }
  
  &[data-checked] {
    color: var(--mantine-color-gray-8);
    background-color: var(--mantine-color-primary-1);
    font-weight: 500;
    svg {
      color: var(--mantine-color-primary-8);
      width: 16px;
      height: 12px;
    }
  }
}

.dropdown {
  --popover-shadow: var(--mantine-shadow-sm);
  --mantine-shadow-sm: 8px 8px 20px 0px rgba(134, 142, 149, 0.18); // Updated shadow value
  border: unset;
}