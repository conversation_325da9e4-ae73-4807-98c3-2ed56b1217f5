/* eslint-disable style/multiline-ternary */
'use client';

import type { ICountry } from 'models/data/types';
import type { Control, UseFormSetValue } from 'react-hook-form';
import type { IError } from 'utils/types';
import type { ProfileFormValues } from './schema';
import { Button, Card, Flex, Grid, Stack, Title, useMatches } from '@mantine/core';
import { DateInputField, NumberPhoneField, RadioField, SelectField, TextAreaField, TextField } from 'components/Form';
import React from 'react';
import { useWatch } from 'react-hook-form';
import { GENDER } from 'utils/constants';

type Props = {
  control: Control<ProfileFormValues>;
  onSubmit?: () => void;
  isLoading?: boolean;
  countryList?: ICountry[];
  isLoadingCountries?: boolean;
  countriesError?: IError | null;
  onSetValue?: UseFormSetValue<ProfileFormValues>;
};

const ProfileForm = ({ control, onSubmit, isLoading, countryList, isLoadingCountries, countriesError, onSetValue, ...props }: Props) => {
  const {
    countryCodeDefault,
  }: Partial<{
    countryCodeDefault: string;
  }> = props;
  const isMobile = useMatches({
    base: true,
    sm: false,
  });
  // Watch the allergy enabled field for conditional rendering
  const allergyEnabled = useWatch({
    control,
    name: 'alleryInformation.enabled',
  });

  return (
    <>
      <Card
        shadow="2px 2px 4px 0px rgba(12, 13, 13, 0.08)"
        style={{
          width: isMobile ? 'calc(100% - 32px)' : '100%',
          backgroundColor: '#FFFFFF',
          border: 'none',
        }}
        p={0}
        radius="8px"
      >
        {/* Header */}
        <Flex
          align="center"
          justify="center"
          p={isMobile ? '16px 24px' : '20px 24px'}
          style={{
            borderBottom: isMobile ? '0.5px solid #DDE3E9' : '1px solid #DDE3E9',
          }}
        >
          <Title
            order={3}
            style={{
              fontWeight: 700,
              fontSize: 24,
              lineHeight: '34px',
              color: '#393F47',
              margin: 0,
            }}
          >
            Edit Profile
          </Title>
        </Flex>

        {/* Form Content */}
        <Stack
          gap={24}
          p={isMobile ? '0px 16px 24px' : '32px'}
          style={{
            width: '100%',
            maxWidth: isMobile ? '100%' : 712,
            margin: '0 auto',
          }}
        >

          {/* Name Fields */}
          {isMobile ? (
          // Mobile: Vertical Stack
            <>
              <TextField
                control={control}
                label="First Name"
                name="firstName"
                placeholder="First Name"
                maxLength={50}
              />

              <TextField
                control={control}
                label="Last Name"
                name="lastName"
                placeholder="Last Name"
                maxLength={50}
              />
            </>
          ) : (
          // Desktop: Grid Layout
            <Grid gutter={16}>
              <Grid.Col span={6}>
                <TextField
                  control={control}
                  label="First Name"
                  name="firstName"
                  placeholder="First Name"
                  maxLength={50}
                />
              </Grid.Col>
              <Grid.Col span={6}>
                <TextField
                  control={control}
                  label="Last Name"
                  name="lastName"
                  placeholder="Last Name"
                  maxLength={50}
                />
              </Grid.Col>
            </Grid>
          )}

          {/* Date of Birth and Gender */}
          {isMobile ? (
          // Mobile: Vertical Stack
            <>
              <DateInputField
                control={control}
                label="Date of Birth"
                name="dob"
                placeholder="Select your birthday"
              />

              <SelectField
                control={control}
                label="Gender"
                name="gender"
                placeholder="Select Gender"
                data={GENDER.map(item => ({
                  value: item.value,
                  label: item.label,
                }))}
              />
            </>
          ) : (
            // Desktop: Grid Layout
            <Grid gutter={16}>
              <Grid.Col span={6}>
                <DateInputField
                  control={control}
                  label="Date of Birth"
                  name="dob"
                  placeholder="Select your birthday"
                />
              </Grid.Col>
              <Grid.Col span={6}>
                <SelectField
                  control={control}
                  label="Gender"
                  name="gender"
                  placeholder="Select Gender"
                  data={GENDER.map(item => ({
                    value: item.value,
                    label: item.label,
                  }))}
                />
              </Grid.Col>
            </Grid>
          )}

          {/* Country and Phone */}
          {isMobile ? (
            // Mobile: Vertical Stack
            <>
              <SelectField
                control={control}
                label="Country of Residence"
                name="countryId"
                placeholder={isLoadingCountries ? 'Loading countries...' : countriesError ? 'Error loading countries' : 'Select country of residence'}
                data={countryList && countryList.length > 0 ? countryList.map(country => ({
                  value: `${country._id}`,
                  label: country.name,
                })) : []}
                disabled={isLoadingCountries || !!countriesError}
              />

              <NumberPhoneField
                control={control}
                label="Contact Phone Number"
                name="phone"
                placeholder="Enter phone number"
                defaultCountryCode={countryCodeDefault}
                onChangeCountry={value => onSetValue && onSetValue('countryCode', value)}
              />
            </>
          ) : (
            // Desktop: Grid Layout
            <Grid gutter={16}>
              <Grid.Col span={6}>
                <SelectField
                  control={control}
                  label="Country of Residence"
                  name="countryId"
                  placeholder={isLoadingCountries ? 'Loading countries...' : countriesError ? 'Error loading countries' : 'Select country of residence'}
                  data={countryList && countryList.length > 0 ? countryList.map(country => ({
                    value: `${country._id}`,
                    label: country.name,
                  })) : []}
                  disabled={isLoadingCountries || !!countriesError}
                />
              </Grid.Col>
              <Grid.Col span={6}>
                <NumberPhoneField
                  control={control}
                  label="Contact Phone Number"
                  name="phone"
                  placeholder="Enter phone number"
                  defaultCountryCode={countryCodeDefault}
                  onChangeCountry={value => onSetValue && onSetValue('countryCode', value)}
                />
              </Grid.Col>
            </Grid>
          )}

          {/* Allergy Information Section */}
          <RadioField
            control={control}
            label="Allergies"
            name="alleryInformation.enabled"
            options={[
              { value: 'false', label: 'No, I don’t have any allergies' },
              { value: 'true', label: 'Yes, I have allergies' },
            ]}
            orientation="vertical"
          />
          {/* Conditionally render allergy information textarea */}
          {allergyEnabled === 'true' && (
            <Stack
              w="100%"
              maw={{
                base: '100%',
                sm: 'calc(50% - 8px)',
              }}
              mt={-16}
            >
              <TextAreaField
                control={control}
                name="alleryInformation.information"
                placeholder="Input"
                maxLength={100}
                required
              />
            </Stack>
          )}
        </Stack>
      </Card>
      <Card
        shadow="2px 2px 4px 0px rgba(12, 13, 13, 0.08)"
        style={{
          width: '100%',
          backgroundColor: '#FFFFFF',
          border: 'none',
        }}
        p={0}
        radius="8px"
      >
        {onSubmit && (
          <Flex
            p="24px 32px"
          >
            <Button
              fullWidth
              size="lg"
              disabled={isLoading}
              loading={isLoading}
              onClick={onSubmit}
            >
              Save
            </Button>
          </Flex>
        )}
      </Card>
    </>
  );
};

export default ProfileForm;
