'use client';

import type { TUserData } from 'models/auth/types';
import type { ICountry } from 'models/data/types';
import type { IError } from 'utils/types';
import { yupResolver } from '@hookform/resolvers/yup';
import { Flex, Loader } from '@mantine/core';
import ProfileForm from 'app/my-page/profile/ProfileFormCard';
import schema, { type ProfileFormValues } from 'app/my-page/profile/schema';
import dayjs from 'dayjs';
import { useFetch, useMutate, useUser } from 'hooks';
import useManualUser from 'hooks/useManualUser';
import { omit } from 'lodash';
import authQuery from 'models/auth';
import dataQuery from 'models/data';
import React, { useCallback, useEffect, useMemo, useState } from 'react';
import { useForm } from 'react-hook-form';
import { DIAL_CODE_TO_COUNTRY } from 'utils/constants';

const ProfilePage = () => {
  const { fetchUser } = useManualUser();
  const [isMobile, setIsMobile] = useState(false);

  useEffect(() => {
    const checkMobile = () => {
      setIsMobile(window.innerWidth < 768);
    };

    checkMobile();
    window.addEventListener('resize', checkMobile);

    return () => window.removeEventListener('resize', checkMobile);
  }, []);
  // Form setup with better default values
  const formDefaultValues = useMemo(() => ({
    firstName: '',
    lastName: '',
    dob: '',
    gender: '',
    countryId: '',
    phone: '',
    countryCode: '',
    allergyInformationEnabled: false,
    allergyInformation: '',
  }), []);
  const { control, handleSubmit, reset, setValue } = useForm<ProfileFormValues>({
    resolver: yupResolver(schema),
    mode: 'onBlur',
    defaultValues: formDefaultValues,
  });
  // Fetch current user data to populate the form
  const { data: user, isLoading: isLoadingUser } = useUser<TUserData>(); // Populate form when user data is available
  useEffect(() => {
    if (user) {
      const formData: ProfileFormValues = {
        firstName: user.firstName || '',
        lastName: user.lastName || '',
        dob: user.dob ? dayjs(user.dob).format('YYYY-MM-DD') : '',
        gender: user.gender || '',
        countryId: user?.country?.countryId ? String(user.country.countryId) : '',
        phone: user?.phone?.phoneNumber || '',
        countryCode: user?.phone?.countryCode || '',
        alleryInformation: {
          enabled: user?.alleryInformation?.enabled ? String(user?.alleryInformation?.enabled) : 'false',
          information: user?.alleryInformation?.information || '',
        },
      };
      reset(formData);
    }
  }, [user, reset]);

  // Create proper country code default for phone field
  const countryCodeDefault = useMemo(() => {
    // If user has existing phone country code, use it
    if (user?.phone?.countryCode) {
      const dialCode = user.phone.countryCode;

      // Try to determine country code from dial code mapping
      const countryCode = DIAL_CODE_TO_COUNTRY[dialCode];
      if (countryCode) {
        return `${countryCode} ${dialCode}`;
      }

      // Last fallback: use the dial code with a generic country code
      return `XX ${dialCode}`;
    }

    // Default fallback - use Japan as default (common in many apps)
    return '+81';
  }, [user?.phone?.countryCode]);

  // Fetch countries with error handling
  const {
    data: countries,
    isLoading: isLoadingCountries,
    error: countriesError,
  } = useFetch<{ token: string }, { docs: ICountry[] }, IError>(
    dataQuery.getAllCountries(),
  );
  const countryList = useMemo(() => countries?.docs || [], [countries?.docs]);

  // API mutation for profile update with enhanced error handling
  const { mutateAsync: updateProfile, isPending } = useMutate<
    Partial<Omit<ProfileFormValues, 'countryCode' | 'countryId' | 'phone' | 'alleryInformation'> & {
      countryId: number;
      phone: { phoneNumber: string; countryCode: string };
      alleryInformation: { enabled: boolean; information?: string };
    }>,
    ProfileFormValues,
    IError
  >(authQuery.updateProfileUser());

  const onSubmit = useCallback(async (values: ProfileFormValues) => {
    const countryId = Number(values.countryId);
    // Ensure countryCode is a non-undefined string
    const countryCode = values.countryCode ?? '';
    const phoneNumber = values?.phone?.replaceAll(countryCode, '') ?? '';

    const submissionData = {
      ...values,
      countryId,
      dob: values.dob ? dayjs(values.dob).format('YYYY-MM-DD') : '',
      phone: {
        phoneNumber,
        countryCode,
      },
      alleryInformation: {
        enabled: !!(values?.alleryInformation?.enabled === 'true'),
        ...(!!(values?.alleryInformation?.enabled === 'true') && { information: values?.alleryInformation?.information || '' }),
      },
    };

    await updateProfile({ ...omit(submissionData, ['countryCode']) }, {
      onSuccess: async () => {
        await fetchUser();
      },
    });
  }, [fetchUser, updateProfile]);

  return (
    <Flex
      align="center"
      direction="column"
      gap={16}
      h="100%"
      justify="center"
      px={isMobile ? 0 : 16}
      pt={16}
      pb={isMobile ? 0 : 16}
      w="100%"
      style={{
        maxWidth: !isMobile ? 760 : '100%',
        margin: 'auto',
      }}
    >
      {isLoadingUser
        ? <Loader />
        : (
            <ProfileForm
              control={control}
              onSubmit={handleSubmit(onSubmit)}
              isLoading={isPending || isLoadingUser}
              countryList={countryList}
              isLoadingCountries={isLoadingCountries}
              countriesError={countriesError}
              onSetValue={setValue}
              {...{ countryCodeDefault }}
            />
          )}
    </Flex>
  );
};

export default ProfilePage;
