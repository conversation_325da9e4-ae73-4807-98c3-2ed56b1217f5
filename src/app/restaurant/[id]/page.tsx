/* eslint-disable react-dom/no-missing-button-type */
'use client';

import type { MenuApiResponse, Restaurant, RestaurantMenu } from 'models/restaurant/types';
import { Box, Container, Flex, Loader, Stack, Text, Title } from '@mantine/core';
import {
  ChefImageCarousel,
  CourseCard,
  FloatingCourseButton,
  ImageGallery,
  InfoRow,
  SectionHeader,
  StickyNavigation,
} from 'components/Restaurant';
import LeftSidebar from 'components/Restaurant/LeftSidebar';
import useFetch from 'hooks/useFetch';
import restaurantQuery from 'models/restaurant';
import { useParams } from 'next/navigation';
import { useEffect, useState } from 'react';
import classes from './styles.module.scss';

const formatTime = (time: string) => {
  return time;
};

// Expandable text component with inline "See more"
const ExpandableText = ({ text, maxLines = 3 }: { text?: string; maxLines?: number }) => {
  const [isExpanded, setIsExpanded] = useState(false);
  const [isMobile, setIsMobile] = useState(false);

  useEffect(() => {
    const checkMobile = () => {
      setIsMobile(window.innerWidth <= 768);
    };

    checkMobile();
    window.addEventListener('resize', checkMobile);
    return () => window.removeEventListener('resize', checkMobile);
  }, []);

  if (!text) {
    return null;
  }

  // Calculate text that fits in 3 lines minus space for "... See more"
  // Mobile: ~40 chars per line, Desktop: ~70 chars per line
  const charsPerLine = isMobile ? 40 : 70;
  const seeMoreText = '... See more';
  const seeMoreLength = seeMoreText.length;
  const totalAvailableChars = maxLines * charsPerLine;
  const truncateLength = totalAvailableChars - seeMoreLength;
  const shouldTruncate = text.length > truncateLength;

  const displayText = shouldTruncate && !isExpanded
    ? text.substring(0, truncateLength).trim()
    : text;

  return (
    <Box mt={{ base: 16, md: 24 }}>
      <div
        style={{
          lineHeight: '22px',
          fontSize: '14px',
          color: '#495057',
          textAlign: 'justify',
          wordBreak: 'break-word',
          fontFamily: 'Roboto, sans-serif',
          fontWeight: 400,
        }}
      >
        {displayText}
        {shouldTruncate && !isExpanded && (
          <span>
            ...
            {' '}
            <button
              onClick={() => setIsExpanded(true)}
              style={{
                background: 'none',
                border: 'none',
                color: '#495057',
                fontSize: '14px',
                fontWeight: 400,
                cursor: 'pointer',
                padding: '0',
                textDecoration: 'underline',
                transition: 'color 0.2s ease',
                fontFamily: 'Roboto, sans-serif',
              }}
            >
              See more
            </button>
          </span>
        )}
        {isExpanded && shouldTruncate && (
          <span>
            {' '}
            <button
              onClick={() => setIsExpanded(false)}
              style={{
                background: 'none',
                border: 'none',
                color: '#495057',
                fontSize: '14px',
                fontWeight: 400,
                cursor: 'pointer',
                padding: '0',
                textDecoration: 'underline',
                transition: 'color 0.2s ease',
                fontFamily: 'Roboto, sans-serif',
              }}
            >
              See less
            </button>
          </span>
        )}
      </div>
    </Box>
  );
};

const RestaurantDetailPage = () => {
  const params = useParams();

  const restaurantId = params.id as string;

  // Fetch restaurant detail data
  const { data: restaurant, isLoading, error } = useFetch<Restaurant>({
    queryKey: ['restaurant-detail', restaurantId],
    ...restaurantQuery.getRestaurantDetail(restaurantId),
  });

  // Fetch restaurant menus
  const { data: menuResponse } = useFetch<MenuApiResponse>({
    queryKey: ['restaurant-menus', restaurantId],
    ...restaurantQuery.getRestaurantMenus(restaurantId),
    enabled: !!restaurantId,
  });

  const menus: RestaurantMenu[] = menuResponse?.docs || [];

  if (isLoading) {
    return (
      <Container size="lg" py={40}>
        <Flex justify="center" align="center" h={400}>
          <Loader size="lg" />
        </Flex>
      </Container>
    );
  }

  if (error || !restaurant) {
    return (
      <Container size="lg" py={40}>
        <Flex justify="center" align="center" h={400}>
          <Stack align="center" gap={16}>
            <Title order={2}>Restaurant not found</Title>
            <Text>The restaurant you're looking for doesn't exist or has been removed.</Text>
          </Stack>
        </Flex>
      </Container>
    );
  }

  const restaurantImages = restaurant.images?.map(img => img.originUrl) || [];
  const dishImages = restaurant.dishImages?.map((img: { originUrl: any }) => img.originUrl) || [];
  const chefImages = restaurant.chefImages?.map((img: { originUrl: any }) => img.originUrl) || [];
  const topImages = restaurant.topImages?.map(img => img.originUrl) || [];
  const sellingPointImages = restaurant.sellingPointImages?.map((img: { originUrl: any }) => img.originUrl) || [];
  const fullAddress = `${restaurant.address.prefectureName}, ${restaurant.address.cityName}`;

  return (
    <Box>
      {/* Hero Image */}
      <div
        className={`${classes.heroImage} hero-section`}
        style={{
          backgroundImage: topImages[0] ? `url(${topImages[0]})` : 'none',
        }}
      />

      {/* Sticky Navigation */}
      <StickyNavigation restaurantNameEn={restaurant.nameEn || restaurant.name} />

      {/* Left Sidebar - Desktop only */}
      <LeftSidebar restaurantNameEn={restaurant.nameEn || restaurant.name} />

      <Container size="lg" py={48}>
        <Stack gap={44} align="center" style={{ width: '100%', maxWidth: '704px', margin: '0 auto' }}>
          {/* About Restaurant Section */}
          <Stack align="center" gap={24} w="100%" maw={704} id="about-restaurant">
            <SectionHeader
              title={`About ${restaurant.nameEn || restaurant.name}`}
              subtitle={restaurant.name !== restaurant.nameEn ? restaurant.name : undefined}
            />

            <Stack gap={8} w="100%">
              <ImageGallery images={restaurantImages.slice(0, 6)} altPrefix="Restaurant" />
              <ExpandableText
                text={restaurant.description || restaurant.descriptionEn}
              />
            </Stack>
          </Stack>

          {/* About Dish Section */}
          <Stack align="center" gap={24} w="100%" maw={704} id="about-dish">
            <SectionHeader title="About Dish" />

            <Stack gap={8} w="100%">
              <ImageGallery images={dishImages.slice(0, 6)} altPrefix="Dish" />
              <ExpandableText
                text={restaurant.dishDescription}
              />
            </Stack>
          </Stack>

          {/* About Chef Section */}
          <Stack align="center" gap={24} w="100%" maw={704} id="about-chef">
            <SectionHeader title="About Chef" />

            <ChefImageCarousel images={chefImages} />

            <ExpandableText
              text={restaurant.aboutChef || restaurant.aboutChefEn || 'Welcome to our refined dining destination, where Japanese tradition meets modern elegance in the heart of Tokyo. Our restaurant offers an exclusive culinary experience, thoughtfully designed to celebrate the finest seasonal ingredients sourced from Japan. Here, each dish is a reflection of both nature\'s rhythm and the chef\'s artistry, prepared with precision and presented with care. At the core of our offering is the omakase experience — a chef\'s tasting menu where guests entrust the selection of courses to the chef\'s expertise. The menu evolves daily, guided by the freshest market ingredients and the chef\'s creative vision. From ...'}
            />
          </Stack>

          {/* Selling Point Section */}
          <Stack align="center" gap={24} w="100%" maw={704} id="selling-point">
            <SectionHeader title="Selling Point" />

            <Stack gap={8} w="100%">
              <ImageGallery images={sellingPointImages.slice(0, 6)} altPrefix="Selling Point" />
              <ExpandableText
                text={restaurant.sellingPointDescription}
              />
            </Stack>
          </Stack>

          {/* Course Section */}
          <Stack align="center" gap={24} w="100%" maw={704} id="course-section">
            <SectionHeader title="Course" />
            {
              menus.length > 0
                ? (
                    <Stack gap={16} w="100%">
                      {menus.map(menu => (
                        <CourseCard key={menu._id} menu={menu} restaurant={restaurant} />
                      ))}
                    </Stack>
                  )
                : (
                    <Text c="gray.6" ta="center">
                      No menu information available at this time.
                    </Text>
                  )
            }
          </Stack>

          {/* Restaurant Information Section */}
          <Stack align="center" gap={24} w="100%" maw={704} id="restaurant-information">
            <SectionHeader title={restaurant.nameEn || restaurant.name} />

            <div className={classes.infoTable} style={{ width: '100%' }}>
              <InfoRow label="Genre" value={restaurant.genre?.name || 'Not specified'} />
              <InfoRow label="Phone number" value={restaurant.phoneNumber || 'Not available'} />
              <InfoRow
                label="Address"
                value={(
                  <Stack gap={16}>
                    <Text>
                      {restaurant.addressEn}
                      <br />
                      {fullAddress}
                    </Text>
                    <div className={classes.mapContainer}>
                      <iframe
                        src={`https://maps.google.com/maps?q=${encodeURIComponent(restaurant.addressEn || fullAddress)}&t=&z=15&ie=UTF8&iwloc=&output=embed`}
                        allowFullScreen
                        loading="lazy"
                        referrerPolicy="no-referrer-when-downgrade"
                        title={`Map of ${restaurant.nameEn || restaurant.name}`}
                        sandbox="allow-scripts allow-same-origin allow-popups allow-forms"
                      />
                    </div>
                  </Stack>
                )}
              />
              <InfoRow
                label="Access Information"
                value={restaurant.accessInformationEn || restaurant.accessInformation}
              />
              <InfoRow
                label="Serving Time"
                value={(
                  <div className={classes.servingTime}>
                    <span>
                      Lunch:
                      {' '}
                      {restaurant.lunchTime ? `${formatTime(restaurant.lunchTime.from)} ~ ${formatTime(restaurant.lunchTime.to)}` : '-'}
                      {' '}
                      /
                    </span>
                    <span>
                      Dinner:
                      {' '}
                      {restaurant.dinnerTime ? `${formatTime(restaurant.dinnerTime.from)} ~ ${formatTime(restaurant.dinnerTime.to)}` : '-'}
                    </span>
                  </div>
                )}
              />
              <InfoRow
                label="Smoking / Non-Smoking"
                value={restaurant.smokingPolicy || 'Non-smoking'}
              />
              <InfoRow
                label="Parking Lot"
                value={restaurant.parkingInfo || 'Available, 5 cars'}
              />
            </div>
          </Stack>
        </Stack>
      </Container>

      {/* Floating Course Button - Mobile only */}
      <FloatingCourseButton />
    </Box>
  );
};

export default RestaurantDetailPage;
