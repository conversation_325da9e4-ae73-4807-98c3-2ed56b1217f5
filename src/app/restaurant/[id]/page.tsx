'use client';

import type { Restaurant } from 'models/restaurant/types';
import ClockIcon from '@icons/icon-calendar.svg';
import IconSun from '@icons/icon-sun.svg';
import IconNext from '@icons/next-icon.svg';
import IconPrev from '@icons/prev-icon.svg';
import { Box, Container, Flex, Group, Image, Loader, Stack, Text, Title } from '@mantine/core';
import useFetch from 'hooks/useFetch';
import restaurantQuery from 'models/restaurant';
import { useParams } from 'next/navigation';
import { type Key, useState } from 'react';
import classes from './styles.module.scss';

// Chef Image Carousel component
const ChefImageCarousel = ({ images }: { images: string[] }) => {
  const [currentIndex, setCurrentIndex] = useState(0);
  const [touchStart, setTouchStart] = useState<number | null>(null);
  const [touchEnd, setTouchEnd] = useState<number | null>(null);

  if (!images.length) {
    return null;
  }

  const handlePrevious = () => {
    setCurrentIndex(prev => (prev === 0 ? images.length - 1 : prev - 1));
  };

  const handleNext = () => {
    setCurrentIndex(prev => (prev === images.length - 1 ? 0 : prev + 1));
  };

  const handleDotClick = (index: number) => {
    setCurrentIndex(index);
  };

  // Touch handlers for mobile swipe
  const onTouchStart = (e: React.TouchEvent) => {
    setTouchEnd(null);
    setTouchStart(e.targetTouches[0]?.clientX || 0);
  };

  const onTouchMove = (e: React.TouchEvent) => {
    setTouchEnd(e.targetTouches[0]?.clientX || 0);
  };

  const onTouchEnd = () => {
    if (!touchStart || !touchEnd) {
      return;
    }
    const distance = touchStart - touchEnd;
    const isLeftSwipe = distance > 50;
    const isRightSwipe = distance < -50;

    if (isLeftSwipe && images.length > 1) {
      handleNext();
    } else if (isRightSwipe && images.length > 1) {
      handlePrevious();
    }
  };

  return (
    <Box w="100%" maw={704} pos="relative" mx="auto">
      <div
        className={classes.chefImageContainer}
        onTouchStart={onTouchStart}
        onTouchMove={onTouchMove}
        onTouchEnd={onTouchEnd}
      >
        <Image
          src={images[currentIndex]}
          alt={`Chef image ${currentIndex + 1}`}
          w="100%"
          h={{ base: 400, md: 528 }}
          radius="md"
          style={{ objectFit: 'contain' }}
        />

        {/* Navigation arrows - Desktop only */}
        {images.length > 1 && (
          <div className={classes.navigationArrows}>
            <button
              className={classes.navButton}
              onClick={handlePrevious}
              aria-label="Previous image"
            >
              <IconPrev />
            </button>
            <button
              className={classes.navButton}
              onClick={handleNext}
              aria-label="Next image"
            >
              <IconNext />
            </button>
          </div>
        )}

        {/* Pagination dots */}
        {images.length > 1 && (
          <div className={classes.paginationDots}>
            {images.map((_, index) => (
              <button
                key={index}
                className={`${classes.dot} ${index === currentIndex ? classes.active : classes.inactive}`}
                onClick={() => handleDotClick(index)}
                aria-label={`Go to image ${index + 1}`}
              />
            ))}
          </div>
        )}
      </div>
    </Box>
  );
};

// Image gallery component
const ImageGallery = ({ images }: { images: string[] }) => {
  const renderImageLayout = () => {
    const imageCount = images.length;

    if (imageCount === 0) {
      return null;
    }

    if (imageCount === 1) {
      return (
        <Box h={400}>
          <Image
            src={images[0]}
            alt="Restaurant"
            w="230px"
            h="172px"
            radius="md"
            style={{ objectFit: 'cover' }}
          />
        </Box>
      );
    }

    if (imageCount === 2) {
      return (
        <Flex gap={8} h={400}>
          {images.slice(0, 2).map((img, idx) => (
            <Box key={idx} flex={1}>
              <Image
                src={img}
                alt="Restaurant"
                w="230px"
                h="172px"
                radius="md"
                style={{ objectFit: 'contain' }}
              />
            </Box>
          ))}
        </Flex>
      );
    }

    if (imageCount === 3) {
      return (
        <Flex gap={8} h={400}>
          {images.slice(0, 3).map((img, idx) => (
            <Box key={idx} flex={1}>
              <Image
                src={img}
                alt="Restaurant"
                w="100%"
                h="100%"
                radius="md"
                style={{ objectFit: 'contain' }}
              />
            </Box>
          ))}
        </Flex>
      );
    }

    if (imageCount === 4) {
      return (
        <Stack gap={8}>
          <Box h={192}>
            <Image
              src={images[0]}
              alt="Restaurant"
              w="100%"
              h="100%"
              radius="md"
              style={{ objectFit: 'contain' }}
            />
          </Box>
          <Flex gap={8} h={192}>
            {images.slice(1, 4).map((img, idx) => (
              <Box key={idx} flex={1}>
                <Image
                  src={img}
                  alt="Restaurant"
                  w="100%"
                  h="100%"
                  radius="md"
                  style={{ objectFit: 'contain' }}
                />
              </Box>
            ))}
          </Flex>
        </Stack>
      );
    }

    // Default case for 5+ images
    return (
      <Stack gap={8}>
        <Flex gap={8} h={172}>
          {images.slice(0, 2).map((img, idx) => (
            <Box key={idx} flex={1}>
              <Image
                src={img}
                alt="Restaurant"
                w="100%"
                h="100%"
                radius="md"
                style={{ objectFit: 'cover' }}
              />
            </Box>
          ))}
        </Flex>
        <Flex gap={8} h={172}>
          {images.slice(2, 5).map((img, idx) => (
            <Box key={idx} flex={1}>
              <Image
                src={img}
                alt="Restaurant"
                w="100%"
                h="100%"
                radius="md"
                style={{ objectFit: 'cover' }}
              />
            </Box>
          ))}
        </Flex>
      </Stack>
    );
  };

  return (
    <div className={classes.imageGallery}>
      {renderImageLayout()}
    </div>
  );
};

// Expandable text component
const ExpandableText = ({ text, maxLines = 8 }: { text?: string; maxLines?: number }) => {
  const [isExpanded, setIsExpanded] = useState(false);

  if (!text) {
    return null;
  }

  // Better line estimation based on average characters per line
  const avgCharsPerLine = 80; // Estimate for 14px font
  const estimatedLines = Math.ceil(text.length / avgCharsPerLine);
  const needsExpansion = estimatedLines > maxLines;

  return (
    <Box>
      <Text
        className={classes.expandableText}
        style={{
          display: '-webkit-box',
          WebkitLineClamp: isExpanded ? 'unset' : maxLines,
          WebkitBoxOrient: 'vertical',
          overflow: 'hidden',
          lineHeight: '22px',
          fontSize: '14px',
          color: '#111D2F',
          textAlign: 'justify',
          wordBreak: 'break-word',
        }}
      >
        {text}
      </Text>
      {needsExpansion && (
        <Box mt={8} style={{ textAlign: 'right' }}>
          <button
            className={classes.showMoreButton}
            onClick={() => setIsExpanded(!isExpanded)}
            style={{
              background: 'none',
              border: 'none',
              color: '#BC923A',
              fontSize: '14px',
              fontWeight: 500,
              cursor: 'pointer',
              padding: '4px 0',
              textDecoration: 'underline',
              transition: 'color 0.2s ease',
            }}
            onMouseEnter={(e) => {
              e.currentTarget.style.color = '#A8832F';
            }}
            onMouseLeave={(e) => {
              e.currentTarget.style.color = '#BC923A';
            }}
          >
            {isExpanded ? 'Show Less ↑' : 'Show More ↓'}
          </button>
        </Box>
      )}
    </Box>
  );
};

// Section header component
const SectionHeader = ({ title, subtitle }: { title: string; subtitle?: string }) => (
  <div className={classes.sectionHeader}>
    <h2 className={classes.sectionTitle}>{title}</h2>
    {subtitle && (
      <p className={classes.sectionSubtitle}>{subtitle}</p>
    )}
    <div className={classes.sectionDivider} />
  </div>
);

// Course card component
const CourseCard = ({ course: _course }: { course: any }) => (
  <div className={classes.courseCard}>
    <Stack gap={16}>
      {/* Course header */}
      <div className={classes.courseHeader}>
        <div className={classes.courseMealTime}>
          <IconSun style={{ width: 16, height: 16 }} />
          <span className={classes.courseMealLabel}>Lunch:</span>
          <span className={classes.courseMealValue}>11:00 ~ 14:00</span>
        </div>

        <span className={classes.courseTitle}>Chef's choice course A</span>
      </div>

      {/* Duration */}
      <div className={classes.courseDuration}>
        <ClockIcon style={{ width: 16, height: 16 }} />
        <span>Course Duration:</span>
        <span>60 minutes</span>
      </div>

      {/* Description */}
      <ExpandableText
        text="Welcome to our refined dining destination, where Japanese tradition meets modern elegance in the heart of Tokyo. Our restaurant offers an exclusive culinary experience, thoughtfully designed to celebrate the finest seasonal ingredients sourced from across Japan. Lorem Ipsum is simply dummy text ..."
      />

      {/* Price and reserve button */}
      <div className={classes.coursePriceSection}>
        <Stack flex={1} gap={2}>
          <span className={classes.coursePriceLabel}>Price Per Seat</span>
          <Group gap={8}>
            <span className={classes.coursePrice}>¥30,000</span>
            <span className={classes.coursePriceUsd}>≈ $206.88</span>
          </Group>
        </Stack>
        <button className={classes.reserveButton}>
          Reserve
        </button>
      </div>
    </Stack>
  </div>
);

// Info table row component
const InfoRow = ({ label, value }: { label: string; value: string | React.ReactNode }) => (
  <div className={classes.infoRow}>
    <div className={classes.infoLabel}>{label}</div>
    <div className={classes.infoValue}>{value}</div>
  </div>
);

const formatTime = (time: string) => {
  return time;
};

const RestaurantDetailPage = () => {
  const params = useParams();
  const restaurantId = params.id as string;

  // Fetch restaurant detail data
  const { data: restaurant, isLoading, error } = useFetch<Restaurant>({
    queryKey: ['restaurant-detail', restaurantId],
    ...restaurantQuery.getRestaurantDetail(restaurantId),
    // For demo purposes, provide fallback data if API fails
    placeholderData: {
      sellingPointDescription: 'What sets us apart is our commitment to using only the finest seasonal ingredients, sourced directly from trusted suppliers across Japan.',
      sellingPointImages: [],
      chefImages: [],
      dishImages: [],
      dishDescription: 'Discover the exquisite dishes at our culinary haven, where each plate tells a story of tradition and innovation.',
      _id: restaurantId,
      id: restaurantId,
      name: 'Sushi Yoshitake',
      nameEn: 'Sushi Yoshitake',
      phoneNumber: '0901111111111',
      address: {
        postalCode: '100-1000',
        prefectureId: 13,
        prefectureName: 'Tokyo',
        cityId: 1,
        cityName: 'Shibuya-ku',
        wardId: 1,
        wardName: 'Sakuragaoka',
        buildingRoom: '1-2-3',
      },
      addressEn: '1-2-3 Sakuragaoka, Shibuya-ku, Tokyo, 100-1000',
      accessInformation: '5 minutes walk from Shibuya Station',
      accessInformationEn: '5 minutes walk from Shibuya Station',
      genre: {
        genreId: 1,
        name: 'Sushi',
      },
      images: [
        {
          key: 'hero',
          originUrl: 'https://images.unsplash.com/photo-1579584425555-c3ce17fd4351?w=1440&h=1080&fit=crop&crop=center',
          remarks: 'Hero image',
        },
        {
          key: 'gallery1',
          originUrl: 'https://images.unsplash.com/photo-1551218808-94e220e084d2?w=400&h=300&fit=crop&crop=center',
          remarks: 'Gallery image 1',
        },
        {
          key: 'gallery2',
          originUrl: 'https://images.unsplash.com/photo-1554118811-1e0d58224f24?w=400&h=300&fit=crop&crop=center',
          remarks: 'Gallery image 2',
        },
        {
          key: 'gallery3',
          originUrl: 'https://images.unsplash.com/photo-1565299624946-b28f40a0ca4b?w=400&h=300&fit=crop&crop=center',
          remarks: 'Gallery image 3',
        },
      ],
      lunchTime: {
        from: '11:00',
        to: '14:00',
      },
      dinnerTime: {
        from: '17:00',
        to: '21:00',
      },
      lowestMenuPrice: 15000,
      highestMenuPrice: 30000,
      lowestMenuPriceUSD: 103,
      highestMenuPriceUSD: 207,
      description: 'Welcome to our refined dining destination, where Japanese tradition meets modern elegance in the heart of Tokyo. Our restaurant offers an exclusive culinary experience, thoughtfully designed to celebrate the finest seasonal ingredients sourced from across Japan.',
      aboutDish: 'Discover the exquisite dishes at our culinary haven, where each plate tells a story of tradition and innovation. Our signature dishes include the melt-in-your-mouth Toro nigiri and delicate Uni served on seasoned rice.',
      aboutChef: 'Our head chef brings over 20 years of experience in traditional Japanese cuisine, combining time-honored techniques with innovative presentations.',
      sellingPoint: 'What sets us apart is our commitment to using only the finest seasonal ingredients, sourced directly from trusted suppliers across Japan.',
      smokingPolicy: 'Non-smoking',
      parkingInfo: 'Available, 5 cars',
    } as Restaurant,
  });

  if (isLoading) {
    return (
      <Container size="lg" py={40}>
        <Flex justify="center" align="center" h={400}>
          <Loader size="lg" />
        </Flex>
      </Container>
    );
  }

  if (error || !restaurant) {
    return (
      <Container size="lg" py={40}>
        <Flex justify="center" align="center" h={400}>
          <Stack align="center" gap={16}>
            <Title order={2}>Restaurant not found</Title>
            <Text>The restaurant you're looking for doesn't exist or has been removed.</Text>
          </Stack>
        </Flex>
      </Container>
    );
  }

  const restaurantImages = restaurant.images?.map(img => img.originUrl) || [];
  const dishImages = restaurant.dishImages?.map((img: { originUrl: any }) => img.originUrl) || [];
  const chefImages = restaurant.chefImages?.map((img: { originUrl: any }) => img.originUrl) || [];
  const topImages = restaurant.topImages?.map(img => img.originUrl) || [];
  const sellingPointImages = restaurant.sellingPointImages?.map((img: { originUrl: any }) => img.originUrl) || [];
  const fullAddress = `${restaurant.address.prefectureName}, ${restaurant.address.cityName}`;

  return (
    <Box>
      {/* Hero Image */}
      <div
        className={classes.heroImage}
        style={{
          backgroundImage: topImages[0] ? `url(${topImages[0]})` : 'none',
        }}
      />

      <Container size="lg" py={48}>
        <Stack gap={44} align="center">
          {/* About Restaurant Section */}
          <Stack align="center" gap={24} w="100%" maw={704}>
            <SectionHeader
              title={`About ${restaurant.nameEn || restaurant.name}`}
              subtitle={restaurant.name !== restaurant.nameEn ? restaurant.name : undefined}
            />

            <Stack gap={8} w="100%">
              <ImageGallery images={restaurantImages.slice(0, 6)} />
              <ExpandableText
                text={restaurant.description || restaurant.descriptionEn || 'Welcome to our refined dining destination, where Japanese tradition meets modern elegance in the heart of Tokyo. Our restaurant offers an exclusive culinary experience, thoughtfully designed to celebrate the finest seasonal ingredients sourced from across Japan. Here, each dish is a reflection of both nature\'s rhythm and the chef\'s artistry, prepared with precision and presented with care. At the core of our offering is the omakase experience — a chef\'s tasting menu where guests entrust the selection of courses to the chef\'s expertise. The menu evolves daily, guided by the freshest market ingredients and the chef\'s creative vision. From ...'}
              />
            </Stack>
          </Stack>

          {/* About Dish Section */}
          <Stack align="center" gap={24} w="100%" maw={704}>
            <SectionHeader title="About Dish" />

            <Stack gap={8} w="100%">
              <ImageGallery images={dishImages.slice(0, 6)} />
              <ExpandableText
                text={restaurant.dishDescription}
              />
            </Stack>
          </Stack>

          {/* About Chef Section */}
          <Stack align="center" gap={24} w="100%" maw={704}>
            <SectionHeader title="About Chef" />

            <ChefImageCarousel images={chefImages} />

            <ExpandableText
              text={restaurant.aboutChef || restaurant.aboutChefEn || 'Welcome to our refined dining destination, where Japanese tradition meets modern elegance in the heart of Tokyo. Our restaurant offers an exclusive culinary experience, thoughtfully designed to celebrate the finest seasonal ingredients sourced from Japan. Here, each dish is a reflection of both nature\'s rhythm and the chef\'s artistry, prepared with precision and presented with care. At the core of our offering is the omakase experience — a chef\'s tasting menu where guests entrust the selection of courses to the chef\'s expertise. The menu evolves daily, guided by the freshest market ingredients and the chef\'s creative vision. From ...'}
            />
          </Stack>

          {/* Selling Point Section */}
          <Stack align="center" gap={24} w="100%" maw={704}>
            <SectionHeader title="Selling Point" />

            <Stack gap={8} w="100%">
              <div className={classes.sellingPointImages}>
                {sellingPointImages.slice(0, 6).map((img: unknown, idx: Key | null | undefined) => (
                  <div key={idx} className={idx === 0 ? classes.sellingPointImageLarge : classes.sellingPointImageSmall}>
                    <Image
                      src={img}
                      alt="Selling Point"
                      w="100%"
                      h="100%"
                      radius="md"
                      // style={{ objectFit: 'cover' }}
                    />
                  </div>
                ))}
              </div>
              <ExpandableText
                text={restaurant.sellingPointDescription}
              />
            </Stack>
          </Stack>

          {/* Course Section */}
          <Stack align="center" gap={24} w="100%" maw={704}>
            <SectionHeader title="Course" />

            <Stack gap={16} w="100%">
              {/* Mock course data - in real app this would come from API */}
              <CourseCard course={{}} />
              <CourseCard course={{}} />
              <CourseCard course={{}} />
            </Stack>
          </Stack>

          {/* Restaurant Information Section */}
          <Stack align="center" gap={24} w="100%" maw={704}>
            <SectionHeader title={restaurant.nameEn || restaurant.name} />

            <div className={classes.infoTable} style={{ width: '100%' }}>
              <InfoRow label="Genre" value={restaurant.genre?.name || 'Not specified'} />
              <InfoRow label="Phone number" value={restaurant.phoneNumber || 'Not available'} />
              <InfoRow
                label="Address"
                value={(
                  <Stack gap={16}>
                    <Text>
                      {restaurant.addressEn}
                      <br />
                      {fullAddress}
                    </Text>
                    <iframe
                      title={`Map showing location of ${restaurant.nameEn || restaurant.name}`}
                      src={`https://www.google.com/maps/embed/v1/place?key=${process.env.NEXT_PUBLIC_GOOGLE_MAPS_API_KEY}&q=${encodeURIComponent(restaurant.addressEn || fullAddress)}`}
                      width="100%"
                      height="218"
                      style={{ border: 0, borderRadius: '4px' }}
                      allowFullScreen
                      loading="lazy"
                      referrerPolicy="no-referrer-when-downgrade"
                    />
                  </Stack>
                )}
              />
              <InfoRow
                label="Access Information"
                value={restaurant.accessInformationEn || restaurant.accessInformation}
              />
              <InfoRow
                label="Serving Time"
                value={`Lunch: ${restaurant.lunchTime ? `${formatTime(restaurant.lunchTime.from)} ~ ${formatTime(restaurant.lunchTime.to)}` : '-'} / Dinner: ${restaurant.dinnerTime ? `${formatTime(restaurant.dinnerTime.from)} ~ ${formatTime(restaurant.dinnerTime.to)}` : '-'}`}
              />
              <InfoRow
                label="Smoking / Non-Smoking"
                value={restaurant.smokingPolicy || 'Non-smoking'}
              />
              <InfoRow
                label="Parking Lot"
                value={restaurant.parkingInfo || 'Available, 5 cars'}
              />
            </div>
          </Stack>
        </Stack>
      </Container>
    </Box>
  );
};

export default RestaurantDetailPage;
