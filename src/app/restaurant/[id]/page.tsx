/* eslint-disable react-dom/no-missing-button-type */
'use client';

import type { MenuApiResponse, Restaurant, RestaurantMenu } from 'models/restaurant/types';
import IconChef from '@icons/icon-chef.svg';
import ClockIcon from '@icons/icon-clock.svg';
import IconGenre from '@icons/icon-genre.svg';
import IconMoon from '@icons/icon-moon.svg';
import IconSun from '@icons/icon-sun.svg';
import IconNext from '@icons/next-icon.svg';
import IconPrev from '@icons/prev-icon.svg';
import { Box, Button, Container, Flex, Group, Image, Loader, Stack, Text, ThemeIcon, Title } from '@mantine/core';
import { ImageGallery } from 'components/Restaurant';
import { StickyNavigation } from 'components/StickyNavigation';
import { useUser } from 'hooks';
import useFetch from 'hooks/useFetch';
import restaurantQuery from 'models/restaurant';
import { useParams, useRouter } from 'next/navigation';
import { useState } from 'react';
import classes from './styles.module.scss';

// Chef Image Carousel component
const ChefImageCarousel = ({ images }: { images: string[] }) => {
  const [currentIndex, setCurrentIndex] = useState(0);
  const [touchStart, setTouchStart] = useState<number | null>(null);
  const [touchEnd, setTouchEnd] = useState<number | null>(null);

  if (!images.length) {
    return null;
  }

  const handlePrevious = () => {
    setCurrentIndex(prev => (prev === 0 ? images.length - 1 : prev - 1));
  };

  const handleNext = () => {
    setCurrentIndex(prev => (prev === images.length - 1 ? 0 : prev + 1));
  };

  const handleDotClick = (index: number) => {
    setCurrentIndex(index);
  };

  // Touch handlers for mobile swipe
  const onTouchStart = (e: React.TouchEvent) => {
    setTouchEnd(null);
    setTouchStart(e.targetTouches[0]?.clientX || 0);
  };

  const onTouchMove = (e: React.TouchEvent) => {
    setTouchEnd(e.targetTouches[0]?.clientX || 0);
  };

  const onTouchEnd = () => {
    if (!touchStart || !touchEnd) {
      return;
    }
    const distance = touchStart - touchEnd;
    const isLeftSwipe = distance > 50;
    const isRightSwipe = distance < -50;

    if (isLeftSwipe && images.length > 1) {
      handleNext();
    } else if (isRightSwipe && images.length > 1) {
      handlePrevious();
    }
  };

  return (
    <Box w="100%" maw={704} pos="relative" mx="auto">
      <div
        className={classes.chefImageContainer}
        onTouchStart={onTouchStart}
        onTouchMove={onTouchMove}
        onTouchEnd={onTouchEnd}
      >
        <Image
          src={images[currentIndex]}
          alt={`Chef image ${currentIndex + 1}`}
          w="100%"
          h={{ base: 400, md: 528 }}
          radius="md"
          style={{ objectFit: 'contain' }}
        />

        {/* Navigation arrows - Desktop only */}
        {images.length > 1 && (
          <div className={classes.navigationArrows}>
            <button
              className={classes.navButton}
              onClick={handlePrevious}
              aria-label="Previous image"
            >
              <IconPrev />
            </button>
            <button
              className={classes.navButton}
              onClick={handleNext}
              aria-label="Next image"
            >
              <IconNext />
            </button>
          </div>
        )}

        {/* Pagination dots */}
        {images.length > 1 && (
          <div className={classes.paginationDots}>
            {images.map((_, index) => (
              <button
                key={index}
                className={`${classes.dot} ${index === currentIndex ? classes.active : classes.inactive}`}
                onClick={() => handleDotClick(index)}
                aria-label={`Go to image ${index + 1}`}
              />
            ))}
          </div>
        )}
      </div>
    </Box>
  );
};

// Course Menu Description component with inline "See more"
const CourseMenuDescription = ({ description }: { description?: string }) => {
  const [isExpanded, setIsExpanded] = useState(false);

  if (!description) {
    return null;
  }

  // Truncate to 500 characters max first
  const maxDescription = description.length > 500
    ? description.substring(0, 500).trim()
    : description;

  // Use character-based truncation for 3 lines (approximately 240 chars for 3 lines)
  const truncateLength = 240;
  const shouldTruncate = maxDescription.length > truncateLength;

  // Get the text to display
  const displayText = isExpanded
    ? maxDescription
    : shouldTruncate
      ? maxDescription.substring(0, truncateLength).trim()
      : maxDescription;

  return (
    <Box>
      <Text
        style={{
          lineHeight: '22px',
          fontSize: '14px',
          color: '#495057',
          textAlign: 'justify',
          wordBreak: 'break-word',
          fontFamily: 'Roboto, sans-serif',
          fontWeight: 400,
        }}
      >
        {displayText}
        {shouldTruncate && !isExpanded && '...'}
        {shouldTruncate && (
          <>
            {' '}
            <button
              onClick={() => setIsExpanded(!isExpanded)}
              style={{
                background: 'none',
                border: 'none',
                color: '#495057',
                fontSize: '14px',
                fontWeight: 400,
                cursor: 'pointer',
                padding: '0',
                textDecoration: 'underline',
                transition: 'color 0.2s ease',
                fontFamily: 'Roboto, sans-serif',
              }}
            >
              {isExpanded ? 'See less' : 'See more'}
            </button>
          </>
        )}
      </Text>
    </Box>
  );
};

// Note: ExpandableText component has been moved outside the function

// Section header component
const SectionHeader = ({ title, subtitle }: { title: string; subtitle?: string }) => (
  <div className={classes.sectionHeader}>
    <h2 className={classes.sectionTitle}>{title}</h2>
    {subtitle && (
      <p className={classes.sectionSubtitle}>{subtitle}</p>
    )}
    <div className={classes.sectionDivider} />
  </div>
);

const CourseCard = ({ menu, restaurant }: { menu: RestaurantMenu; restaurant: Restaurant }) => {
  const { data: user } = useUser();
  const { push } = useRouter();

  const formatMealTime = (menuTimeZone: string) => {
    if (!menuTimeZone) {
      return { icon: <IconSun style={{ width: 16, height: 16 }} />, label: 'All Day:', time: '11:00 ~ 21:00' };
    }

    // Parse menuTimeZone to determine if it's lunch or dinner
    const isLunch = menuTimeZone.toLowerCase().includes('lunch');
    const isDinner = menuTimeZone.toLowerCase().includes('dinner');
    const lunchTimeCourse = restaurant?.lunchTime;
    const dinnerTimeCourse = restaurant?.dinnerTime;

    if (isLunch) {
      return { icon: <IconSun style={{ width: 16, height: 16 }} />, label: 'Lunch:', time: `${lunchTimeCourse?.from} ~ ${lunchTimeCourse?.to}` };
    } else if (isDinner) {
      return { icon: <IconMoon style={{ width: 16, height: 16 }} />, label: 'Dinner:', time: `${dinnerTimeCourse?.from} ~ ${dinnerTimeCourse?.to}` };
    }
    // Default fallback for other cases
    return { icon: <IconSun style={{ width: 16, height: 16 }} />, label: 'All Day:', time: '11:00 ~ 21:00' };
  };

  const mealTimeInfo = formatMealTime(menu.menuTimeZone || '');

  // Validate required fields
  const hasValidData = menu && menu.name && typeof menu.price === 'number' && typeof menu.priceUSD === 'number';

  if (!hasValidData) {
    return (
      <div className={classes.courseCard}>
        <Text c="red" ta="center">Invalid menu data</Text>
      </div>
    );
  }

  const handleReserve = () => {
    const restaurantId = restaurant._id;
    if (user) {
      // User is authenticated, go to booking page
      push(`/my-page/restaurant/${restaurantId}/booking`);
    } else {
      // User not authenticated, store intended destination and go to login
      if (typeof window !== 'undefined') {
        sessionStorage.setItem('redirectAfterLogin', `/my-page/restaurant/${restaurantId}/booking`);
      }
      push('/login');
    }
  };

  return (
    <div className={classes.courseCard}>
      <Stack gap={16}>
        {/* Course header */}
        <div className={classes.courseHeader} style={{ width: 'fit-content' }}>
          <div className={classes.courseMealTime}>
            {mealTimeInfo.icon}
            <span className={classes.courseMealLabel}>{mealTimeInfo.label}</span>
            <span className={classes.courseMealValue}>{mealTimeInfo.time}</span>
          </div>
        </div>

        {/* Course title with chef icon */}
        <div style={{ display: 'flex', alignItems: 'flex-end', gap: '8px' }}>
          <ThemeIcon size={44}>
            <IconChef />
          </ThemeIcon>
          <span className={classes.courseTitle}>{menu.name}</span>
        </div>

        {/* Duration */}
        {menu.duration && (
          <div className={classes.courseDuration}>
            <ThemeIcon size={16}>
              <ClockIcon />
            </ThemeIcon>
            <span>Course Duration:</span>
            <span>
              {menu.duration}
              {' '}
              minutes
            </span>
          </div>
        )}

        {/* Description with CourseMenuDescription component */}
        <CourseMenuDescription description={menu.description} />

        {/* Price and reserve button */}
        <div className={classes.coursePriceSection}>
          <Stack flex={1} gap={2}>
            <span className={classes.coursePriceLabel}>Price Per Seat</span>
            <Group gap={8}>
              <span className={classes.coursePrice}>
                JPY
                {' '}
                {menu.price?.toLocaleString() || '0'}
              </span>
              <span className={classes.coursePriceUsd}>
                (
                {menu.priceUSD?.toFixed(2) || '0.00'}
                {' '}
                USD
                )
              </span>
            </Group>
          </Stack>
          <Button className={classes.reserveButton} leftSection={<IconGenre />} onClick={handleReserve}>
            Reserve
          </Button>
        </div>
      </Stack>
    </div>
  );
};

// Info table row component
const InfoRow = ({ label, value }: { label: string; value: string | React.ReactNode }) => (
  <div className={classes.infoRow}>
    <div className={classes.infoLabel}>{label}</div>
    <div className={classes.infoValue}>{value}</div>
  </div>
);

const formatTime = (time: string) => {
  return time;
};

// Expandable text component with inline "See more"
const ExpandableText = ({ text, maxLines = 8 }: { text?: string; maxLines?: number }) => {
  const [isExpanded, setIsExpanded] = useState(false);

  if (!text) {
    return null;
  }

  // Estimate characters per line based on maxLines (approximately 80 chars per line)
  const truncateLength = maxLines * 80;
  const shouldTruncate = text.length > truncateLength;

  // Get the text to display
  const displayText = isExpanded
    ? text
    : shouldTruncate
      ? text.substring(0, truncateLength).trim()
      : text;

  return (
    <Box mt={{ base: 16, md: 24 }}>
      {isExpanded
        ? (
            <Text
              style={{
                lineHeight: '22px',
                fontSize: '14px',
                color: '#495057',
                textAlign: 'justify',
                wordBreak: 'break-word',
                fontFamily: 'Roboto, sans-serif',
                fontWeight: 400,
              }}
            >
              {text}
              {shouldTruncate && (
                <button
                  onClick={() => setIsExpanded(!isExpanded)}
                  style={{
                    background: 'none',
                    border: 'none',
                    color: '#495057',
                    fontSize: '14px',
                    fontWeight: 400,
                    cursor: 'pointer',
                    padding: '0',
                    textDecoration: 'underline',
                    transition: 'color 0.2s ease',
                    marginLeft: '4px',
                    fontFamily: 'Roboto, sans-serif',
                  }}
                >
                  See less
                </button>
              )}
            </Text>
          )
        : (
            <Text
              style={{
                lineHeight: '22px',
                fontSize: '14px',
                color: '#495057',
                textAlign: 'justify',
                wordBreak: 'break-word',
                fontFamily: 'Roboto, sans-serif',
                fontWeight: 400,
              }}
            >
              {displayText}
              {shouldTruncate && (
                <>
                  ...
                  {' '}
                  <button
                    onClick={() => setIsExpanded(!isExpanded)}
                    style={{
                      background: 'none',
                      border: 'none',
                      color: '#495057',
                      fontSize: '14px',
                      fontWeight: 400,
                      cursor: 'pointer',
                      padding: '0',
                      textDecoration: 'underline',
                      transition: 'color 0.2s ease',
                      fontFamily: 'Roboto, sans-serif',
                    }}
                  >
                    See more
                  </button>
                </>
              )}
            </Text>
          )}
    </Box>
  );
};

const RestaurantDetailPage = () => {
  const params = useParams();

  const restaurantId = params.id as string;

  // Fetch restaurant detail data
  const { data: restaurant, isLoading, error } = useFetch<Restaurant>({
    queryKey: ['restaurant-detail', restaurantId],
    ...restaurantQuery.getRestaurantDetail(restaurantId),
  });

  // Fetch restaurant menus
  const { data: menuResponse } = useFetch<MenuApiResponse>({
    queryKey: ['restaurant-menus', restaurantId],
    ...restaurantQuery.getRestaurantMenus(restaurantId),
    enabled: !!restaurantId,
  });

  const menus: RestaurantMenu[] = menuResponse?.docs || [];
  // eslint-disable-next-line no-console
  console.log('menus', menuResponse, menus);
  if (isLoading) {
    return (
      <Container size="lg" py={40}>
        <Flex justify="center" align="center" h={400}>
          <Loader size="lg" />
        </Flex>
      </Container>
    );
  }

  if (error || !restaurant) {
    return (
      <Container size="lg" py={40}>
        <Flex justify="center" align="center" h={400}>
          <Stack align="center" gap={16}>
            <Title order={2}>Restaurant not found</Title>
            <Text>The restaurant you're looking for doesn't exist or has been removed.</Text>
          </Stack>
        </Flex>
      </Container>
    );
  }

  const restaurantImages = restaurant.images?.map(img => img.originUrl) || [];
  const dishImages = restaurant.dishImages?.map((img: { originUrl: any }) => img.originUrl) || [];
  const chefImages = restaurant.chefImages?.map((img: { originUrl: any }) => img.originUrl) || [];
  const topImages = restaurant.topImages?.map(img => img.originUrl) || [];
  const sellingPointImages = restaurant.sellingPointImages?.map((img: { originUrl: any }) => img.originUrl) || [];
  const fullAddress = `${restaurant.address.prefectureName}, ${restaurant.address.cityName}`;

  return (
    <Box>
      {/* Hero Image */}
      <div
        className={`${classes.heroImage} hero-section`}
        style={{
          backgroundImage: topImages[0] ? `url(${topImages[0]})` : 'none',
        }}
      />

      {/* Sticky Navigation */}
      <StickyNavigation />

      <Container size="lg" py={48}>
        <Stack gap={44} align="center">
          {/* About Restaurant Section */}
          <Stack align="center" gap={24} w="100%" maw={704} id="about-restaurant">
            <SectionHeader
              title={`About ${restaurant.nameEn || restaurant.name}`}
              subtitle={restaurant.name !== restaurant.nameEn ? restaurant.name : undefined}
            />

            <Stack gap={8} w="100%">
              <ImageGallery images={restaurantImages.slice(0, 6)} altPrefix="Restaurant" />
              <ExpandableText
                text={restaurant.description || restaurant.descriptionEn}
              />
            </Stack>
          </Stack>

          {/* About Dish Section */}
          <Stack align="center" gap={24} w="100%" maw={704} id="about-dish">
            <SectionHeader title="About Dish" />

            <Stack gap={8} w="100%">
              <ImageGallery images={dishImages.slice(0, 6)} altPrefix="Dish" />
              <ExpandableText
                text={restaurant.dishDescription}
              />
            </Stack>
          </Stack>

          {/* About Chef Section */}
          <Stack align="center" gap={24} w="100%" maw={704} id="about-chef">
            <SectionHeader title="About Chef" />

            <ChefImageCarousel images={chefImages} />

            <ExpandableText
              text={restaurant.aboutChef || restaurant.aboutChefEn || 'Welcome to our refined dining destination, where Japanese tradition meets modern elegance in the heart of Tokyo. Our restaurant offers an exclusive culinary experience, thoughtfully designed to celebrate the finest seasonal ingredients sourced from Japan. Here, each dish is a reflection of both nature\'s rhythm and the chef\'s artistry, prepared with precision and presented with care. At the core of our offering is the omakase experience — a chef\'s tasting menu where guests entrust the selection of courses to the chef\'s expertise. The menu evolves daily, guided by the freshest market ingredients and the chef\'s creative vision. From ...'}
            />
          </Stack>

          {/* Selling Point Section */}
          <Stack align="center" gap={24} w="100%" maw={704} id="selling-point">
            <SectionHeader title="Selling Point" />

            <Stack gap={8} w="100%">
              <ImageGallery images={sellingPointImages.slice(0, 6)} altPrefix="Selling Point" />
              <ExpandableText
                text={restaurant.sellingPointDescription}
              />
            </Stack>
          </Stack>

          {/* Course Section */}
          <Stack align="center" gap={24} w="100%" maw={704} id="course-section">
            <SectionHeader title="Course" />
            {
              menus.length > 0
                ? (
                    <Stack gap={16} w="100%">
                      {menus.map(menu => (
                        <CourseCard key={menu._id} menu={menu} restaurant={restaurant} />
                      ))}
                    </Stack>
                  )
                : (
                    <Text c="gray.6" ta="center">
                      No menu information available at this time.
                    </Text>
                  )
            }
          </Stack>

          {/* Restaurant Information Section */}
          <Stack align="center" gap={24} w="100%" maw={704} id="restaurant-information">
            <SectionHeader title={restaurant.nameEn || restaurant.name} />

            <div className={classes.infoTable} style={{ width: '100%' }}>
              <InfoRow label="Genre" value={restaurant.genre?.name || 'Not specified'} />
              <InfoRow label="Phone number" value={restaurant.phoneNumber || 'Not available'} />
              <InfoRow
                label="Address"
                value={(
                  <Stack gap={16}>
                    <Text>
                      {restaurant.addressEn}
                      <br />
                      {fullAddress}
                    </Text>
                    {/* <iframe
                      title={`Map showing location of ${restaurant.nameEn || restaurant.name}`}
                      src={`https://www.google.com/maps/embed/v1/place?key=${process.env.NEXT_PUBLIC_GOOGLE_MAPS_API_KEY}&q=${encodeURIComponent(restaurant.addressEn || fullAddress)}`}
                      width="100%"
                      height="218"
                      style={{ border: 0, borderRadius: '4px' }}
                      allowFullScreen
                      loading="lazy"
                      referrerPolicy="no-referrer-when-downgrade"
                    /> */}
                  </Stack>
                )}
              />
              <InfoRow
                label="Access Information"
                value={restaurant.accessInformationEn || restaurant.accessInformation}
              />
              <InfoRow
                label="Serving Time"
                value={`Lunch: ${restaurant.lunchTime ? `${formatTime(restaurant.lunchTime.from)} ~ ${formatTime(restaurant.lunchTime.to)}` : '-'} / Dinner: ${restaurant.dinnerTime ? `${formatTime(restaurant.dinnerTime.from)} ~ ${formatTime(restaurant.dinnerTime.to)}` : '-'}`}
              />
              <InfoRow
                label="Smoking / Non-Smoking"
                value={restaurant.smokingPolicy || 'Non-smoking'}
              />
              <InfoRow
                label="Parking Lot"
                value={restaurant.parkingInfo || 'Available, 5 cars'}
              />
            </div>
          </Stack>
        </Stack>
      </Container>
    </Box>
  );
};

export default RestaurantDetailPage;
