/* eslint-disable react-dom/no-missing-button-type */
'use client';

import type { MenuApiResponse, Restaurant, RestaurantMenu } from 'models/restaurant/types';
import IconChef from '@icons/icon-chef.svg';
import ClockIcon from '@icons/icon-clock.svg';
import IconGenre from '@icons/icon-genre.svg';
import IconMoon from '@icons/icon-moon.svg';
import IconSun from '@icons/icon-sun.svg';
import IconNext from '@icons/next-icon.svg';
import IconPrev from '@icons/prev-icon.svg';
import { Box, Button, Container, Flex, Group, Image, Loader, Spoiler, Stack, Text, ThemeIcon, Title } from '@mantine/core';
import useFetch from 'hooks/useFetch';
import restaurantQuery from 'models/restaurant';
import { useParams } from 'next/navigation';
import { type Key, useEffect, useState } from 'react';
import classes from './styles.module.scss';

// Chef Image Carousel component
const ChefImageCarousel = ({ images }: { images: string[] }) => {
  const [currentIndex, setCurrentIndex] = useState(0);
  const [touchStart, setTouchStart] = useState<number | null>(null);
  const [touchEnd, setTouchEnd] = useState<number | null>(null);

  if (!images.length) {
    return null;
  }

  const handlePrevious = () => {
    setCurrentIndex(prev => (prev === 0 ? images.length - 1 : prev - 1));
  };

  const handleNext = () => {
    setCurrentIndex(prev => (prev === images.length - 1 ? 0 : prev + 1));
  };

  const handleDotClick = (index: number) => {
    setCurrentIndex(index);
  };

  // Touch handlers for mobile swipe
  const onTouchStart = (e: React.TouchEvent) => {
    setTouchEnd(null);
    setTouchStart(e.targetTouches[0]?.clientX || 0);
  };

  const onTouchMove = (e: React.TouchEvent) => {
    setTouchEnd(e.targetTouches[0]?.clientX || 0);
  };

  const onTouchEnd = () => {
    if (!touchStart || !touchEnd) {
      return;
    }
    const distance = touchStart - touchEnd;
    const isLeftSwipe = distance > 50;
    const isRightSwipe = distance < -50;

    if (isLeftSwipe && images.length > 1) {
      handleNext();
    } else if (isRightSwipe && images.length > 1) {
      handlePrevious();
    }
  };

  return (
    <Box w="100%" maw={704} pos="relative" mx="auto">
      <div
        className={classes.chefImageContainer}
        onTouchStart={onTouchStart}
        onTouchMove={onTouchMove}
        onTouchEnd={onTouchEnd}
      >
        <Image
          src={images[currentIndex]}
          alt={`Chef image ${currentIndex + 1}`}
          w="100%"
          h={{ base: 400, md: 528 }}
          radius="md"
          style={{ objectFit: 'contain' }}
        />

        {/* Navigation arrows - Desktop only */}
        {images.length > 1 && (
          <div className={classes.navigationArrows}>
            <button
              className={classes.navButton}
              onClick={handlePrevious}
              aria-label="Previous image"
            >
              <IconPrev />
            </button>
            <button
              className={classes.navButton}
              onClick={handleNext}
              aria-label="Next image"
            >
              <IconNext />
            </button>
          </div>
        )}

        {/* Pagination dots */}
        {images.length > 1 && (
          <div className={classes.paginationDots}>
            {images.map((_, index) => (
              <button
                key={index}
                className={`${classes.dot} ${index === currentIndex ? classes.active : classes.inactive}`}
                onClick={() => handleDotClick(index)}
                aria-label={`Go to image ${index + 1}`}
              />
            ))}
          </div>
        )}
      </div>
    </Box>
  );
};

// Image gallery component
const ImageGallery = ({ images }: { images: string[] }) => {
  const [selectedImage, setSelectedImage] = useState<string | null>(null);

  const handleImageClick = (imageUrl: string) => {
    setSelectedImage(imageUrl);
  };

  const closeModal = () => {
    setSelectedImage(null);
  };

  // Handle escape key to close modal
  useEffect(() => {
    const handleEscape = (e: KeyboardEvent) => {
      if (e.key === 'Escape' && selectedImage) {
        closeModal();
      }
    };

    document.addEventListener('keydown', handleEscape);
    return () => document.removeEventListener('keydown', handleEscape);
  }, [selectedImage]);

  if (!images.length) {
    return null;
  }

  const renderImageLayout = () => {
    const imageCount = images.length;

    // Single image layout
    if (imageCount === 1) {
      return (
        <div className={classes.galleryContainer}>
          <div className={classes.singleImageContainer}>
            <Image
              src={images[0]}
              alt="Restaurant image"
              className={classes.galleryImage}
              onClick={() => handleImageClick(images[0] || '')}
              style={{ cursor: 'pointer' }}
            />
          </div>
        </div>
      );
    }

    // Two images layout - side by side
    if (imageCount === 2) {
      return (
        <div className={classes.galleryContainer}>
          <div className={classes.twoImageGrid}>
            {images.slice(0, 2).map((img, idx) => (
              <div key={idx} className={classes.imageWrapper}>
                <Image
                  src={img}
                  alt={`Restaurant image ${idx + 1}`}
                  className={classes.galleryImage}
                  onClick={() => handleImageClick(img)}
                  style={{ cursor: 'pointer' }}
                />
              </div>
            ))}
          </div>
        </div>
      );
    }

    // Three images layout - one large + two small
    if (imageCount === 3) {
      return (
        <div className={classes.galleryContainer}>
          <div className={classes.threeImageGrid}>
            <div className={classes.largeImageWrapper}>
              <Image
                src={images[0]}
                alt="Restaurant image 1"
                className={classes.galleryImage}
                onClick={() => handleImageClick(images[0] || '')}
                style={{ cursor: 'pointer' }}
              />
            </div>
            <div className={classes.smallImagesColumn}>
              {images.slice(1, 3).map((img, idx) => (
                <div key={idx} className={classes.smallImageWrapper}>
                  <Image
                    src={img}
                    alt={`Restaurant image ${idx + 2}`}
                    className={classes.galleryImage}
                    onClick={() => handleImageClick(img)}
                    style={{ cursor: 'pointer' }}
                  />
                </div>
              ))}
            </div>
          </div>
        </div>
      );
    }

    // Four images layout - 2x2 grid
    if (imageCount === 4) {
      return (
        <div className={classes.galleryContainer}>
          <div className={classes.fourImageGrid}>
            {images.slice(0, 4).map((img, idx) => (
              <div key={idx} className={classes.gridImageWrapper}>
                <Image
                  src={img}
                  alt={`Restaurant image ${idx + 1}`}
                  className={classes.galleryImage}
                  onClick={() => handleImageClick(img)}
                  style={{ cursor: 'pointer' }}
                />
              </div>
            ))}
          </div>
        </div>
      );
    }

    // Five or more images layout - featured image + grid with overlay
    return (
      <div className={classes.galleryContainer}>
        <div className={classes.multiImageGrid}>
          {/* Main large image */}
          <div className={classes.featuredImageWrapper}>
            <Image
              src={images[0]}
              alt="Featured restaurant image"
              className={classes.galleryImage}
              onClick={() => handleImageClick(images[0] || '')}
              style={{ cursor: 'pointer' }}
            />
          </div>

          {/* Grid of smaller images */}
          <div className={classes.imageGridSection}>
            {images.slice(1, 5).map((img, idx) => (
              <div
                key={idx}
                className={`${classes.gridImageWrapper} ${idx === 3 && images.length > 5 ? classes.overlayWrapper : ''}`}
              >
                <Image
                  src={img}
                  alt={`Restaurant image ${idx + 2}`}
                  className={classes.galleryImage}
                  onClick={() => handleImageClick(img)}
                  style={{ cursor: 'pointer' }}
                />
                {/* Show overlay on last image if there are more images */}
                {idx === 3 && images.length > 5 && (
                  <button
                    className={classes.imageOverlay}
                    onClick={() => handleImageClick(img)}
                    aria-label={`View all ${images.length} images`}
                  >
                    <Text className={classes.overlayText}>
                      +
                      {images.length - 5}
                      {' '}
                      more
                    </Text>
                  </button>
                )}
              </div>
            ))}
          </div>
        </div>
      </div>
    );
  };

  return (
    <>
      <div className={classes.imageGallery}>
        {renderImageLayout()}
      </div>

      {/* Image modal for full-size viewing */}
      {selectedImage && (
        <div
          className={classes.imageModal}
          role="dialog"
          aria-modal="true"
          aria-label="Image viewer"
        >
          <div
            className={classes.modalContent}
            onClick={e => e.stopPropagation()}
          >
            <button
              className={classes.closeButton}
              onClick={closeModal}
              aria-label="Close image viewer"
            >
              ×
            </button>
            <Image
              src={selectedImage}
              alt="Full size restaurant image"
              className={classes.modalImage}
            />
          </div>
          <button
            className={classes.modalBackdrop}
            onClick={closeModal}
            aria-label="Close image viewer"
            style={{
              position: 'absolute',
              top: 0,
              left: 0,
              width: '100%',
              height: '100%',
              background: 'transparent',
              border: 'none',
              cursor: 'pointer',
              zIndex: -1,
            }}
          />
        </div>
      )}
    </>
  );
};

// Course Menu Description component using Mantine Spoiler
const CourseMenuDescription = ({ description }: { description?: string }) => {
  if (!description) {
    return null;
  }

  return (
    <Spoiler
      maxHeight={66} // 3 lines × 22px line-height
      showLabel="See more"
      hideLabel="See less"
      classNames={{
        control: classes.spoilerControl,
      }}
    >
      <Text
        style={{
          lineHeight: '22px',
          fontSize: '14px',
          color: '#111D2F',
          textAlign: 'justify',
          wordBreak: 'break-word',
          fontFamily: 'Roboto, sans-serif',
          fontWeight: 400,
        }}
      >
        {description}
      </Text>
    </Spoiler>
  );
};

// Expandable text component using Mantine Spoiler
const ExpandableText = ({ text, maxLines = 8 }: { text?: string; maxLines?: number }) => {
  if (!text) {
    return null;
  }

  // Calculate maxHeight based on line height and number of lines
  const lineHeight = 22; // 22px line height
  const maxHeight = maxLines * lineHeight;

  return (
    <Spoiler
      maxHeight={maxHeight}
      showLabel="See more"
      hideLabel="See less"
      classNames={{
        control: classes.spoilerControl,
      }}
    >
      <Text
        style={{
          lineHeight: '22px',
          fontSize: '14px',
          color: '#111D2F',
          textAlign: 'justify',
          wordBreak: 'break-word',
          fontFamily: 'Roboto, sans-serif',
          fontWeight: 400,
        }}
      >
        {text}
      </Text>
    </Spoiler>
  );
};

// Section header component
const SectionHeader = ({ title, subtitle }: { title: string; subtitle?: string }) => (
  <div className={classes.sectionHeader}>
    <h2 className={classes.sectionTitle}>{title}</h2>
    {subtitle && (
      <p className={classes.sectionSubtitle}>{subtitle}</p>
    )}
    <div className={classes.sectionDivider} />
  </div>
);

// Course card component with improved data handling and debugging
const CourseCard = ({ menu }: { menu: RestaurantMenu }) => {
  const formatMealTime = (menuTimeZone: string) => {
    if (!menuTimeZone) {
      return { icon: <IconSun style={{ width: 16, height: 16 }} />, label: 'All Day:', time: '11:00 ~ 21:00' };
    }

    // Parse menuTimeZone to determine if it's lunch or dinner
    const isLunch = menuTimeZone.toLowerCase().includes('lunch');
    const isDinner = menuTimeZone.toLowerCase().includes('dinner');

    if (isLunch) {
      return { icon: <IconSun style={{ width: 16, height: 16 }} />, label: 'Lunch:', time: '11:00 ~ 14:00' };
    } else if (isDinner) {
      return { icon: <IconMoon style={{ width: 16, height: 16 }} />, label: 'Dinner:', time: '17:00 ~ 21:00' };
    } else {
      return { icon: <IconSun style={{ width: 16, height: 16 }} />, label: 'All Day:', time: '11:00 ~ 21:00' };
    }
  };

  const mealTimeInfo = formatMealTime(menu.menuTimeZone);

  // Validate required fields
  const hasValidData = menu && menu.name && typeof menu.price === 'number' && typeof menu.priceUSD === 'number';

  if (!hasValidData) {
    return (
      <div className={classes.courseCard}>
        <Text c="red" ta="center">Invalid menu data</Text>
      </div>
    );
  }

  return (
    <div className={classes.courseCard}>
      <Stack gap={16}>
        {/* Course header */}
        <div className={classes.courseHeader} style={{ width: 'fit-content' }}>
          <div className={classes.courseMealTime}>
            {mealTimeInfo.icon}
            <span className={classes.courseMealLabel}>{mealTimeInfo.label}</span>
            <span className={classes.courseMealValue}>{mealTimeInfo.time}</span>
          </div>
        </div>

        {/* Course title with chef icon */}
        <div style={{ display: 'flex', alignItems: 'flex-end', gap: '8px' }}>
          <ThemeIcon size={44}>
            <IconChef />
          </ThemeIcon>
          <span className={classes.courseTitle}>{menu.name}</span>
        </div>

        {/* Duration */}
        {menu.duration && (
          <div className={classes.courseDuration}>
            <ThemeIcon size={16}>
              <ClockIcon />
            </ThemeIcon>
            <span>Course Duration:</span>
            <span>
              {menu.duration}
              {' '}
              minutes
            </span>
          </div>
        )}

        {/* Description with CourseMenuDescription component */}
        <CourseMenuDescription description={menu.description} />

        {/* Price and reserve button */}
        <div className={classes.coursePriceSection}>
          <Stack flex={1} gap={2}>
            <span className={classes.coursePriceLabel}>Price Per Seat</span>
            <Group gap={8}>
              <span className={classes.coursePrice}>
                ¥
                {menu.price?.toLocaleString() || '0'}
              </span>
              <span className={classes.coursePriceUsd}>
                ≈ $
                {menu.priceUSD?.toFixed(2) || '0.00'}
              </span>
            </Group>
          </Stack>
          <Button className={classes.reserveButton} leftSection={<IconGenre />}>
            Reserve
          </Button>
        </div>
      </Stack>
    </div>
  );
};

// Info table row component
const InfoRow = ({ label, value }: { label: string; value: string | React.ReactNode }) => (
  <div className={classes.infoRow}>
    <div className={classes.infoLabel}>{label}</div>
    <div className={classes.infoValue}>{value}</div>
  </div>
);

const formatTime = (time: string) => {
  return time;
};

const RestaurantDetailPage = () => {
  const params = useParams();
  const restaurantId = params.id as string;

  // Fetch restaurant detail data
  const { data: restaurant, isLoading, error } = useFetch<Restaurant>({
    queryKey: ['restaurant-detail', restaurantId],
    ...restaurantQuery.getRestaurantDetail(restaurantId),
    // For demo purposes, provide fallback data if API fails
    placeholderData: {
      sellingPointDescription: 'What sets us apart is our commitment to using only the finest seasonal ingredients, sourced directly from trusted suppliers across Japan.',
      sellingPointImages: [],
      chefImages: [],
      dishImages: [],
      dishDescription: 'Discover the exquisite dishes at our culinary haven, where each plate tells a story of tradition and innovation.',
      _id: restaurantId,
      id: restaurantId,
      name: 'Sushi Yoshitake',
      nameEn: 'Sushi Yoshitake',
      phoneNumber: '0901111111111',
      address: {
        postalCode: '100-1000',
        prefectureId: 13,
        prefectureName: 'Tokyo',
        cityId: 1,
        cityName: 'Shibuya-ku',
        wardId: 1,
        wardName: 'Sakuragaoka',
        buildingRoom: '1-2-3',
      },
      addressEn: '1-2-3 Sakuragaoka, Shibuya-ku, Tokyo, 100-1000',
      accessInformation: '5 minutes walk from Shibuya Station',
      accessInformationEn: '5 minutes walk from Shibuya Station',
      genre: {
        genreId: 1,
        name: 'Sushi',
      },
      images: [
        {
          key: 'hero',
          originUrl: 'https://images.unsplash.com/photo-1579584425555-c3ce17fd4351?w=1440&h=1080&fit=crop&crop=center',
          remarks: 'Hero image',
        },
        {
          key: 'gallery1',
          originUrl: 'https://images.unsplash.com/photo-1551218808-94e220e084d2?w=400&h=300&fit=crop&crop=center',
          remarks: 'Gallery image 1',
        },
        {
          key: 'gallery2',
          originUrl: 'https://images.unsplash.com/photo-1554118811-1e0d58224f24?w=400&h=300&fit=crop&crop=center',
          remarks: 'Gallery image 2',
        },
        {
          key: 'gallery3',
          originUrl: 'https://images.unsplash.com/photo-1565299624946-b28f40a0ca4b?w=400&h=300&fit=crop&crop=center',
          remarks: 'Gallery image 3',
        },
      ],
      lunchTime: {
        from: '11:00',
        to: '14:00',
      },
      dinnerTime: {
        from: '17:00',
        to: '21:00',
      },
      lowestMenuPrice: 15000,
      highestMenuPrice: 30000,
      lowestMenuPriceUSD: 103,
      highestMenuPriceUSD: 207,
      description: 'Welcome to our refined dining destination, where Japanese tradition meets modern elegance in the heart of Tokyo. Our restaurant offers an exclusive culinary experience, thoughtfully designed to celebrate the finest seasonal ingredients sourced from across Japan.',
      aboutDish: 'Discover the exquisite dishes at our culinary haven, where each plate tells a story of tradition and innovation. Our signature dishes include the melt-in-your-mouth Toro nigiri and delicate Uni served on seasoned rice.',
      aboutChef: 'Our head chef brings over 20 years of experience in traditional Japanese cuisine, combining time-honored techniques with innovative presentations.',
      sellingPoint: 'What sets us apart is our commitment to using only the finest seasonal ingredients, sourced directly from trusted suppliers across Japan.',
      smokingPolicy: 'Non-smoking',
      parkingInfo: 'Available, 5 cars',
    } as Restaurant,
  });

  // Fetch restaurant menus
  const { data: menuResponse, isLoading: menusLoading, error: menusError } = useFetch<MenuApiResponse>({
    queryKey: ['restaurant-menus', restaurantId],
    ...restaurantQuery.getRestaurantMenus(restaurantId),
    enabled: !!restaurantId,
  });

  const menus: RestaurantMenu[] = menuResponse?.data?.docs || [];

  // Add test data for debugging when no API data is available
  const testMenus: RestaurantMenu[] = [
    {
      _id: 'test-1',
      name: 'Omakase Premium Course',
      description: 'Experience our chef\'s selection of the finest seasonal ingredients in this premium omakase course. Each dish is carefully crafted to showcase the natural flavors and textures of premium Japanese ingredients. From the delicate sashimi selection to the perfectly seasoned rice dishes, every course represents the pinnacle of Japanese culinary artistry. This multi-course experience takes you on a journey through different flavors, temperatures, and textures, creating an unforgettable dining experience that celebrates both tradition and innovation in Japanese cuisine.',
      menuTimeZone: 'dinner',
      menuType: 'INBOUND_TOURIST',
      duration: 120,
      price: 25000,
      priceUSD: 172.50,
      createdAt: new Date(),
      updatedAt: new Date(),
    },
    {
      _id: 'test-2',
      name: 'Lunch Kaiseki Set',
      description: 'A refined lunch set featuring traditional kaiseki-style presentation with modern touches. Each item is prepared with seasonal ingredients.',
      menuTimeZone: 'lunch',
      menuType: 'INBOUND_TOURIST',
      duration: 90,
      price: 15000,
      priceUSD: 103.50,
      createdAt: new Date(),
      updatedAt: new Date(),
    },
  ];

  // Use real menus if available, otherwise fall back to test data for development
  const displayMenus = menus.length > 0 ? menus : (process.env.NODE_ENV === 'development' ? testMenus : []);

  if (isLoading) {
    return (
      <Container size="lg" py={40}>
        <Flex justify="center" align="center" h={400}>
          <Loader size="lg" />
        </Flex>
      </Container>
    );
  }

  if (error || !restaurant) {
    return (
      <Container size="lg" py={40}>
        <Flex justify="center" align="center" h={400}>
          <Stack align="center" gap={16}>
            <Title order={2}>Restaurant not found</Title>
            <Text>The restaurant you're looking for doesn't exist or has been removed.</Text>
          </Stack>
        </Flex>
      </Container>
    );
  }

  const restaurantImages = restaurant.images?.map(img => img.originUrl) || [];
  const dishImages = restaurant.dishImages?.map((img: { originUrl: any }) => img.originUrl) || [];
  const chefImages = restaurant.chefImages?.map((img: { originUrl: any }) => img.originUrl) || [];
  const topImages = restaurant.topImages?.map(img => img.originUrl) || [];
  const sellingPointImages = restaurant.sellingPointImages?.map((img: { originUrl: any }) => img.originUrl) || [];
  const fullAddress = `${restaurant.address.prefectureName}, ${restaurant.address.cityName}`;

  return (
    <Box>
      {/* Hero Image */}
      <div
        className={classes.heroImage}
        style={{
          backgroundImage: topImages[0] ? `url(${topImages[0]})` : 'none',
        }}
      />

      <Container size="lg" py={48}>
        <Stack gap={44} align="center">
          {/* About Restaurant Section */}
          <Stack align="center" gap={24} w="100%" maw={704}>
            <SectionHeader
              title={`About ${restaurant.nameEn || restaurant.name}`}
              subtitle={restaurant.name !== restaurant.nameEn ? restaurant.name : undefined}
            />

            <Stack gap={8} w="100%">
              <ImageGallery images={restaurantImages.slice(0, 6)} />
              <ExpandableText
                text={restaurant.description || restaurant.descriptionEn || 'Welcome to our refined dining destination, where Japanese tradition meets modern elegance in the heart of Tokyo. Our restaurant offers an exclusive culinary experience, thoughtfully designed to celebrate the finest seasonal ingredients sourced from across Japan. Here, each dish is a reflection of both nature\'s rhythm and the chef\'s artistry, prepared with precision and presented with care. At the core of our offering is the omakase experience — a chef\'s tasting menu where guests entrust the selection of courses to the chef\'s expertise. The menu evolves daily, guided by the freshest market ingredients and the chef\'s creative vision. From ...'}
              />
            </Stack>
          </Stack>

          {/* About Dish Section */}
          <Stack align="center" gap={24} w="100%" maw={704}>
            <SectionHeader title="About Dish" />

            <Stack gap={8} w="100%">
              <ImageGallery images={dishImages.slice(0, 6)} />
              <ExpandableText
                text={restaurant.dishDescription}
              />
            </Stack>
          </Stack>

          {/* About Chef Section */}
          <Stack align="center" gap={24} w="100%" maw={704}>
            <SectionHeader title="About Chef" />

            <ChefImageCarousel images={chefImages} />

            <ExpandableText
              text={restaurant.aboutChef || restaurant.aboutChefEn || 'Welcome to our refined dining destination, where Japanese tradition meets modern elegance in the heart of Tokyo. Our restaurant offers an exclusive culinary experience, thoughtfully designed to celebrate the finest seasonal ingredients sourced from Japan. Here, each dish is a reflection of both nature\'s rhythm and the chef\'s artistry, prepared with precision and presented with care. At the core of our offering is the omakase experience — a chef\'s tasting menu where guests entrust the selection of courses to the chef\'s expertise. The menu evolves daily, guided by the freshest market ingredients and the chef\'s creative vision. From ...'}
            />
          </Stack>

          {/* Selling Point Section */}
          <Stack align="center" gap={24} w="100%" maw={704}>
            <SectionHeader title="Selling Point" />

            <Stack gap={8} w="100%">
              <div className={classes.sellingPointImages}>
                {sellingPointImages.slice(0, 6).map((img: unknown, idx: Key | null | undefined) => (
                  <div key={idx} className={idx === 0 ? classes.sellingPointImageLarge : classes.sellingPointImageSmall}>
                    <Image
                      src={img}
                      alt="Selling Point"
                      w="100%"
                      h="100%"
                      radius="md"
                      // style={{ objectFit: 'cover' }}
                    />
                  </div>
                ))}
              </div>
              <ExpandableText
                text={restaurant.sellingPointDescription}
              />
            </Stack>
          </Stack>

          {/* Course Section with improved debugging */}
          <Stack align="center" gap={24} w="100%" maw={704}>
            <SectionHeader title="Course" />

            {menusLoading
              ? (
                  <Flex justify="center" align="center" h={200}>
                    <Stack align="center" gap={16}>
                      <Loader size="md" />
                      <Text size="sm" c="gray.6">Loading menu information...</Text>
                    </Stack>
                  </Flex>
                )
              : menusError
                ? (
                    <Stack align="center" gap={16}>
                      <Text c="red" ta="center">
                        Failed to load menu information. Please try again later.
                      </Text>
                      {process.env.NODE_ENV === 'development' && (
                        <Text size="xs" c="gray.6" ta="center">
                          Error details:
                          {' '}
                          {JSON.stringify(menusError)}
                        </Text>
                      )}
                    </Stack>
                  )
                : displayMenus.length > 0
                  ? (
                      <Stack gap={16} w="100%">
                        {displayMenus.map(menu => (
                          <CourseCard key={menu._id} menu={menu} />
                        ))}
                      </Stack>
                    )
                  : (
                      <Text c="gray.6" ta="center">
                        No menu information available at this time.
                      </Text>
                    )}
          </Stack>

          {/* Restaurant Information Section */}
          <Stack align="center" gap={24} w="100%" maw={704}>
            <SectionHeader title={restaurant.nameEn || restaurant.name} />

            <div className={classes.infoTable} style={{ width: '100%' }}>
              <InfoRow label="Genre" value={restaurant.genre?.name || 'Not specified'} />
              <InfoRow label="Phone number" value={restaurant.phoneNumber || 'Not available'} />
              <InfoRow
                label="Address"
                value={(
                  <Stack gap={16}>
                    <Text>
                      {restaurant.addressEn}
                      <br />
                      {fullAddress}
                    </Text>
                    <iframe
                      title={`Map showing location of ${restaurant.nameEn || restaurant.name}`}
                      src={`https://www.google.com/maps/embed/v1/place?key=${process.env.NEXT_PUBLIC_GOOGLE_MAPS_API_KEY}&q=${encodeURIComponent(restaurant.addressEn || fullAddress)}`}
                      width="100%"
                      height="218"
                      style={{ border: 0, borderRadius: '4px' }}
                      allowFullScreen
                      loading="lazy"
                      referrerPolicy="no-referrer-when-downgrade"
                    />
                  </Stack>
                )}
              />
              <InfoRow
                label="Access Information"
                value={restaurant.accessInformationEn || restaurant.accessInformation}
              />
              <InfoRow
                label="Serving Time"
                value={`Lunch: ${restaurant.lunchTime ? `${formatTime(restaurant.lunchTime.from)} ~ ${formatTime(restaurant.lunchTime.to)}` : '-'} / Dinner: ${restaurant.dinnerTime ? `${formatTime(restaurant.dinnerTime.from)} ~ ${formatTime(restaurant.dinnerTime.to)}` : '-'}`}
              />
              <InfoRow
                label="Smoking / Non-Smoking"
                value={restaurant.smokingPolicy || 'Non-smoking'}
              />
              <InfoRow
                label="Parking Lot"
                value={restaurant.parkingInfo || 'Available, 5 cars'}
              />
            </div>
          </Stack>
        </Stack>
      </Container>
    </Box>
  );
};

export default RestaurantDetailPage;
