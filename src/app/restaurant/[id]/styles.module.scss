.heroImage {
  height: 1080px;
  background-size: cover;
  background-position: center;
  background-color: #f5f5f5;
  position: relative;
  width: 100%;

  @media (max-width: 768px) {
    height: 400px;
  }
}





// Custom styling for Spoiler components to match Figma design
.spoilerControl {
  color: #BC923A !important;
  font-size: 14px !important;
  font-weight: 400 !important;
  font-family: 'Roboto', sans-serif !important;
  line-height: 22px !important;
  text-align: right !important;
  width: 100% !important;
  display: block !important;
  margin-top: 8px !important;
  padding: 0 !important;
  border: none !important;
  background: none !important;
  cursor: pointer !important;
  transition: color 0.2s ease !important;

  &:hover {
    color: #A8832F !important;
  }

  &:focus {
    outline: none !important;
    color: #A8832F !important;
  }
}

.imageGallery {
  width: 100%;
  max-width: 704px;
  margin: 0 auto;
}

// Gallery container styles
.galleryContainer {
  width: 100%;
  border-radius: 8px;
  overflow: hidden;
}

.galleryImage {
  width: 100%;
  height: 100%;
  object-fit: cover;
  border-radius: 8px;
  transition: transform 0.2s ease, box-shadow 0.2s ease;

  &:hover {
    transform: scale(1.02);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  }
}

// Single image layout
.singleImageContainer {
  width: 100%;
  height: 400px;
  display: flex;
  justify-content: center;
  align-items: center;

  @media (max-width: 768px) {
    height: 300px;
  }
}

// Two images layout
.twoImageGrid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 8px;
  height: 350px;

  @media (max-width: 768px) {
    height: 250px;
    gap: 6px;
  }
}

.imageWrapper {
  overflow: hidden;
  border-radius: 8px;
}

// Three images layout
.threeImageGrid {
  display: grid;
  grid-template-columns: 2fr 1fr;
  gap: 8px;
  height: 350px;

  @media (max-width: 768px) {
    grid-template-columns: 1fr;
    height: auto;
    gap: 6px;
  }
}

.largeImageWrapper {
  overflow: hidden;
  border-radius: 8px;
}

.smallImagesColumn {
  display: grid;
  grid-template-rows: 1fr 1fr;
  gap: 8px;

  @media (max-width: 768px) {
    grid-template-columns: 1fr 1fr;
    grid-template-rows: none;
    gap: 6px;
  }
}

.smallImageWrapper {
  overflow: hidden;
  border-radius: 8px;
}

// Four images layout
.fourImageGrid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  grid-template-rows: 1fr 1fr;
  gap: 8px;
  height: 350px;

  @media (max-width: 768px) {
    height: 250px;
    gap: 6px;
  }
}

.gridImageWrapper {
  overflow: hidden;
  border-radius: 8px;
  position: relative;
}

// Map container styles
.mapContainer {
  width: 100%;
  height: 100px;
  position: relative;
  border-radius: 4px;
  overflow: hidden;

  @media (min-width: 768px) {
    height: auto;
    min-height: 300px;
  }

  iframe {
    width: 100%;
    height: 100%;
    border: 0;
    position: absolute;
    top: 0;
    left: 0;
  }
}

// Multi-image layout (5+ images)
.multiImageGrid {
  display: grid;
  grid-template-columns: 2fr 1fr;
  gap: 8px;
  height: 400px;

  @media (max-width: 768px) {
    grid-template-columns: 1fr;
    height: auto;
    gap: 6px;
  }
}

.featuredImageWrapper {
  overflow: hidden;
  border-radius: 8px;
}

.imageGridSection {
  display: grid;
  grid-template-columns: 1fr 1fr;
  grid-template-rows: 1fr 1fr;
  gap: 8px;

  @media (max-width: 768px) {
    grid-template-columns: 1fr 1fr;
    gap: 6px;
  }
}

.overlayWrapper {
  position: relative;
}

.imageOverlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.6);
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 8px;
  cursor: pointer;
  transition: background-color 0.2s ease;
  border: none;

  &:hover {
    background: rgba(0, 0, 0, 0.7);
  }
}

.overlayText {
  color: white;
  font-size: 18px;
  font-weight: 600;
  text-align: center;

  @media (max-width: 768px) {
    font-size: 16px;
  }
}

// Image modal styles
.imageModal {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.9);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  padding: 20px;
}

.modalContent {
  position: relative;
  max-width: 90vw;
  max-height: 90vh;
  display: flex;
  align-items: center;
  justify-content: center;
}

.closeButton {
  position: absolute;
  top: -40px;
  right: 0;
  background: none;
  border: none;
  color: white;
  font-size: 30px;
  cursor: pointer;
  z-index: 1001;
  width: 40px;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;

  &:hover {
    background: rgba(255, 255, 255, 0.1);
    border-radius: 50%;
  }
}

.modalImage {
  max-width: 100%;
  max-height: 100%;
  object-fit: contain;
  border-radius: 8px;
}

.expandableText {
  font-size: 14px;
  line-height: 22px;
  color: #111D2F;
}

.showMoreButton {
  font-size: 14px;
  color: #111D2F;
  cursor: pointer;
  text-align: right;
  margin-top: 8px;
  background: none;
  border: none;
  padding: 0;

  &:hover {
    text-decoration: underline;
  }
}



.infoTable {
  background-color: white;
  border: none;
  border-radius: 4px;
  padding: 24px;
  overflow: hidden;
}



.mapPlaceholder {
  width: 100%;
  height: 218px;
  background-color: #f5f5f5;
  border-radius: 4px;
  background-size: cover;
  background-position: center;
}



// Selling point image styles moved to ImageGallery component

.servingTime {
  display: flex;
  flex-direction: row;
  align-items: center;
  gap: 8px; // Add spacing between elements on desktop
}

.servingTimeSeparator {
  display: inline;
}

// Mobile responsive styles
@media (max-width: 768px) {
  .imageGallery {
    max-width: 100%;
    padding: 0 16px;
  }



  .servingTime {
    display: flex;
    flex-direction: column;
    align-items: flex-start; // Align left on mobile
    gap: 4px; // Small gap between lines on mobile
  }

  .servingTimeSeparator {
    display: inline; // Show separator on mobile too
  }

  // Selling point image mobile styles moved to ImageGallery component
} 