.heroImage {
  height: 1080px;
  background-size: cover;
  background-position: center;
  background-color: #f5f5f5;
  position: relative;
  width: 100%;

  @media (max-width: 768px) {
    height: 400px;
  }
}

.sectionHeader {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 4px;
  margin-bottom: 12px;
}

.sectionTitle {
  font-size: 32px;
  font-weight: 400;
  font-family: 'Merriweather', serif;
  color: #111D2F;
  text-align: center;
  margin: 0;

  @media (max-width: 768px) {
    font-size: 24px;
  }
}

.sectionSubtitle {
  font-size: 18px;
  color: #828282;
  text-align: center;
  margin: 0;

  @media (max-width: 768px) {
    font-size: 16px;
  }
}

.sectionDivider {
  width: 79px;
  height: 2px;
  background-color: #BC923A;
  margin-top: 8px;
}

.imageGallery {
  width: 100%;
  max-width: 704px;
  margin: 0 auto;
}

// Gallery container styles
.galleryContainer {
  width: 100%;
  border-radius: 8px;
  overflow: hidden;
}

.galleryImage {
  width: 100%;
  height: 100%;
  object-fit: cover;
  border-radius: 8px;
  transition: transform 0.2s ease, box-shadow 0.2s ease;

  &:hover {
    transform: scale(1.02);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  }
}

// Single image layout
.singleImageContainer {
  width: 100%;
  height: 400px;
  display: flex;
  justify-content: center;
  align-items: center;

  @media (max-width: 768px) {
    height: 300px;
  }
}

// Two images layout
.twoImageGrid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 8px;
  height: 350px;

  @media (max-width: 768px) {
    height: 250px;
    gap: 6px;
  }
}

.imageWrapper {
  overflow: hidden;
  border-radius: 8px;
}

// Three images layout
.threeImageGrid {
  display: grid;
  grid-template-columns: 2fr 1fr;
  gap: 8px;
  height: 350px;

  @media (max-width: 768px) {
    grid-template-columns: 1fr;
    height: auto;
    gap: 6px;
  }
}

.largeImageWrapper {
  overflow: hidden;
  border-radius: 8px;
}

.smallImagesColumn {
  display: grid;
  grid-template-rows: 1fr 1fr;
  gap: 8px;

  @media (max-width: 768px) {
    grid-template-columns: 1fr 1fr;
    grid-template-rows: none;
    gap: 6px;
  }
}

.smallImageWrapper {
  overflow: hidden;
  border-radius: 8px;
}

// Four images layout
.fourImageGrid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  grid-template-rows: 1fr 1fr;
  gap: 8px;
  height: 350px;

  @media (max-width: 768px) {
    height: 250px;
    gap: 6px;
  }
}

.gridImageWrapper {
  overflow: hidden;
  border-radius: 8px;
  position: relative;
}

// Multi-image layout (5+ images)
.multiImageGrid {
  display: grid;
  grid-template-columns: 2fr 1fr;
  gap: 8px;
  height: 400px;

  @media (max-width: 768px) {
    grid-template-columns: 1fr;
    height: auto;
    gap: 6px;
  }
}

.featuredImageWrapper {
  overflow: hidden;
  border-radius: 8px;
}

.imageGridSection {
  display: grid;
  grid-template-columns: 1fr 1fr;
  grid-template-rows: 1fr 1fr;
  gap: 8px;

  @media (max-width: 768px) {
    grid-template-columns: 1fr 1fr;
    gap: 6px;
  }
}

.overlayWrapper {
  position: relative;
}

.imageOverlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.6);
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 8px;
  cursor: pointer;
  transition: background-color 0.2s ease;
  border: none;

  &:hover {
    background: rgba(0, 0, 0, 0.7);
  }
}

.overlayText {
  color: white;
  font-size: 18px;
  font-weight: 600;
  text-align: center;

  @media (max-width: 768px) {
    font-size: 16px;
  }
}

// Image modal styles
.imageModal {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.9);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  padding: 20px;
}

.modalContent {
  position: relative;
  max-width: 90vw;
  max-height: 90vh;
  display: flex;
  align-items: center;
  justify-content: center;
}

.closeButton {
  position: absolute;
  top: -40px;
  right: 0;
  background: none;
  border: none;
  color: white;
  font-size: 30px;
  cursor: pointer;
  z-index: 1001;
  width: 40px;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;

  &:hover {
    background: rgba(255, 255, 255, 0.1);
    border-radius: 50%;
  }
}

.modalImage {
  max-width: 100%;
  max-height: 100%;
  object-fit: contain;
  border-radius: 8px;
}

.expandableText {
  font-size: 14px;
  line-height: 22px;
  color: #111D2F;
}

.showMoreButton {
  font-size: 14px;
  color: #111D2F;
  cursor: pointer;
  text-align: right;
  margin-top: 8px;
  background: none;
  border: none;
  padding: 0;

  &:hover {
    text-decoration: underline;
  }
}

.courseCard {
  border: 1px solid #E3E3E3;
  border-radius: 4px;
  padding: 24px;
  background-color: white;
  width: 100%;
}

.courseHeader {
  background-color: #F5F5F5;
  border-radius: 4px;
  padding: 8px;
  display: flex;
  align-items: center;
  gap: 16px;
}

.courseMealTime {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 12px;
}

.courseMealLabel {
  color: #828282;
}

.courseMealValue {
  color: #111D2F;
}

.courseTitle {
  font-size: 20px;
  line-height: 32px;
  font-weight: 400;
  color: #BC923A;
  font-family: 'Merriweather', serif;
}

.courseDuration {
  display: flex;
  align-items: center;
  gap: 4px;
  font-size: 12px;
  color: #484848;
}

.coursePriceSection {
  background-color: #FAF7EF;
  border-radius: 4px;
  padding: 12px;
  display: flex;
  align-items: center;
  gap: 8px;
}

.coursePriceLabel {
  font-size: 14px;
  color: #9C7A30;
}

.coursePrice {
  font-size: 20px;
  font-weight: 700;
  color: #111D2F;
}

.coursePriceUsd {
  font-size: 14px;
  color: #828282;
}

.reserveButton {
  background-color: #BC923A;
  color: white;
  border-radius: 4px;
  padding: 12px 16px;
  font-size: 14px;
  font-weight: 500;
  width: 200px;
  border: none;
  cursor: pointer;

  &:hover {
    background-color: #A8832F;
  }
}

.infoTable {
  background-color: white;
  border: 1px solid #E3E3E3;
  border-radius: 4px;
  padding: 24px;
  overflow: hidden;
}

.infoRow {
  display: flex;
  border-bottom: 1px solid #EAEAEA;

  &:last-child {
    border-bottom: none;
  }
}

.infoLabel {
  width: 220px;
  padding: 12px 16px;
  background-color: #FAFAFA;
  border-right: 1px solid #EAEAEA;
  font-size: 14px;
  font-weight: 500;
  color: #828282;
}

.infoValue {
  flex: 1;
  padding: 12px 16px;
  font-size: 14px;
  color: #111D2F;
}

.mapPlaceholder {
  width: 100%;
  height: 218px;
  background-color: #f5f5f5;
  border-radius: 4px;
  background-size: cover;
  background-position: center;
}

.chefImageContainer {
  position: relative;
  width: 100%;
  max-width: 704px;
  height: 528px;
  border-radius: 8px;
  overflow: hidden;
  margin: 0 auto;

  @media (max-width: 768px) {
    height: 400px;
  }
}

.navigationArrows {
  position: absolute;
  top: 50%;
  left: 16px;
  right: 16px;
  transform: translateY(-50%);
  display: flex;
  justify-content: space-between;
  pointer-events: none;

  @media (max-width: 768px) {
    display: none;
  }
}

.navButton {
  background-color: rgba(17, 29, 47, 0.6);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 32px;
  width: 40px;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  cursor: pointer;
  font-size: 18px;
  pointer-events: auto;
  transition: all 0.2s ease;

  &:hover {
    background-color: rgba(17, 29, 47, 0.8);
    transform: scale(1.05);
  }

  &:active {
    transform: scale(0.95);
  }
}

.paginationDots {
  position: absolute;
  bottom: 34px;
  left: 50%;
  transform: translateX(-50%);
  display: flex;
  gap: 6px;
}

.dot {
  width: 10px;
  height: 10px;
  border-radius: 50%;
  border: 1px solid rgba(255, 255, 255, 0.2);
  cursor: pointer;
  transition: all 0.2s ease;
  background: none;
  padding: 0;

  &.active {
    background-color: rgba(255, 255, 255, 0.8);
  }

  &.inactive {
    background-color: rgba(17, 29, 47, 0.6);
  }

  &:hover {
    transform: scale(1.1);
    border-color: rgba(255, 255, 255, 0.4);
  }

  &:active {
    transform: scale(0.9);
  }
}

.sellingPointImages {
  display: flex;
  gap: 8px;
  height: 261px;
}

.sellingPointImageLarge {
  flex: 1;
}

.sellingPointImageSmall {
  width: 348px;
}

// Mobile responsive styles
@media (max-width: 768px) {
  .imageGallery {
    max-width: 100%;
    padding: 0 16px;
  }

  .courseCard {
    padding: 16px;
  }

  .coursePriceSection {
    flex-direction: column;
    align-items: stretch;
    gap: 12px;
  }

  .reserveButton {
    width: 100%;
  }

  .infoLabel {
    width: 150px;
    font-size: 12px;
  }

  .infoValue {
    font-size: 12px;
  }



  .sellingPointImages {
    flex-direction: column;
    height: auto;
  }

  .sellingPointImageLarge,
  .sellingPointImageSmall {
    flex: none;
    width: 100%;
    height: 200px;
  }
} 