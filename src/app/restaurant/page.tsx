'use client';

import type { SearchFilters } from 'components/Restaurant';
import type { Restaurant } from 'models/restaurant/types';
import { Center, Container, Loader, Stack, Text } from '@mantine/core';
import { Pagination, RestaurantCard, SearchSection } from 'components/Restaurant';
import useList from 'hooks/useList';
import restaurantQuery from 'models/restaurant';
import { useRouter } from 'next/navigation';
import { useCallback, useState } from 'react';

// Constants
const ITEMS_PER_PAGE = 10;
const PAGE_TITLE = 'Discover Japan\'s Top Fine Dining';
const NO_RESULTS_TITLE = 'No restaurants found';
const NO_RESULTS_MESSAGE = 'Try adjusting your search criteria to find more results';

const RestaurantPage = () => {
  const router = useRouter();
  const today = new Date();
  const oneMonthLater = new Date(today.getFullYear(), today.getMonth() + 1, today.getDate());
  const [currentPage, setCurrentPage] = useState(1);

  const [searchFilters, setSearchFilters] = useState<SearchFilters>({
    searchQuery: '',
    startDate: today,
    endDate: oneMonthLater,
  });

  // Prepare API query parameters
  const queryParams = {
    page: currentPage,
    limit: ITEMS_PER_PAGE,
    ...(searchFilters.startDate && { fromDate: searchFilters.startDate.toISOString() }),
    ...(searchFilters.endDate && { toDate: searchFilters.endDate.toISOString() }),
  };

  // Fetch restaurants using useList hook
  const {
    list: restaurants,
    total: totalItems,
    totalPages,
    isLoading,
  } = useList<Restaurant>({
    queryKey: ['restaurants', currentPage, searchFilters],
    ...restaurantQuery.getRestaurants(queryParams),
  });

  // Remove client-side filtering - all filtering is now handled server-side via API
  const filteredRestaurants = restaurants;
  const handleSearch = useCallback((filters: SearchFilters) => {
    setSearchFilters(filters);
    setCurrentPage(1); // Reset to first page on new search
  }, []);

  const handlePageChange = useCallback((page: number) => {
    setCurrentPage(page);
    window.scrollTo({ top: 0, behavior: 'smooth' });
  }, []);

  const handleRestaurantClick = useCallback((restaurantId: string) => {
    router.push(`/restaurant/${restaurantId}`);
  }, [router]);

  return (
    <Container size="lg" py={40}>
      <Stack gap={16}>
        {/* Search Section */}
        <SearchSection onSearch={handleSearch} isLoading={isLoading} />

        {/* Page Title */}
        <div style={{
          width: '100%',
          maxWidth: '704px',
          margin: '0 auto',
          fontFamily: 'Merriweather, sans-serif',
        }}
        >
          <Text
            c="gray.9"
            fw={400}
            fz={{
              base: '20px',
              md: '32px',
            }}
          >
            {PAGE_TITLE}
          </Text>
        </div>

        {/* Restaurant Listings */}
        {isLoading
          ? (
              <Center py={60}>
                <Loader color="primary.6" size="lg" />
              </Center>
            )
          : filteredRestaurants.length > 0
            ? (
                <Stack gap={20}>
                  {filteredRestaurants.map((restaurant: Restaurant, index) => (
                    <RestaurantCard
                      key={`${restaurant.name}-${index}`}
                      restaurant={restaurant}
                      onClick={() => handleRestaurantClick(restaurant._id)}
                    />
                  ))}
                </Stack>
              )
            : (
                <Center py={60}>
                  <Stack align="center" gap={16}>
                    <Text
                      size="lg"
                      style={{
                        color: '#828282',
                        fontSize: '18px',
                        fontWeight: 500,
                      }}
                    >
                      {NO_RESULTS_TITLE}
                    </Text>
                    <Text
                      size="md"
                      style={{
                        color: '#BABABA',
                        fontSize: '14px',
                        fontWeight: 400,
                      }}
                    >
                      {NO_RESULTS_MESSAGE}
                    </Text>
                  </Stack>
                </Center>
              )}

        {/* Pagination */}
        {!isLoading && filteredRestaurants.length > 0 && (
          <Pagination
            currentPage={currentPage}
            totalPages={totalPages}
            totalItems={totalItems}
            itemsPerPage={ITEMS_PER_PAGE}
            onPageChange={handlePageChange}
          />
        )}
      </Stack>
    </Container>
  );
};

export default RestaurantPage;
