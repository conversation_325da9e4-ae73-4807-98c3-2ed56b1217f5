import type { MutateOptions } from '@tanstack/react-query';
import type { LoginFormValues } from 'components/Authentication/LoginForm/schema';
import type { SubmitHandler } from 'react-hook-form';
import type { IError } from 'utils/types';
import authQuery from 'models/auth';
import { useRouter } from 'next/navigation';
import { COOKIE_TOKEN_KEY } from 'utils/constants';
import { setCookie } from 'utils/server-cookies';

import useManualUser from './useManualUser';
import useMutate from './useMutate';
import { useAuthChannelStore } from './zustand';

const useLogin = ({
  ...options
}: MutateOptions<
  unknown,
  IError,
  {
    email: string;
    password: string;
  },
  unknown
>) => {
  const { authChannel } = useAuthChannelStore();

  const { mutateAsync: signIn, isPending } = useMutate<
    LoginFormValues,
    unknown,
    IError
  >(authQuery.signIn());
  const { replace } = useRouter();
  const { fetchUser } = useManualUser();

  const handleLogin: SubmitHandler<LoginFormValues> = (values) => {
    // Extract onClose from options to avoid type conflicts
    const { onClose, ...restOptions } = options as { onClose?: () => void };

    signIn(values, {
      onSuccess: async (data) => {
        if (authChannel) {
          authChannel?.postMessage('login');
        }
        await setCookie(COOKIE_TOKEN_KEY, JSON.stringify(data), {
          httpOnly: true,
          maxAge: 7776000000,
          sameSite: 'strict',
        });
        await fetchUser();
        if (authChannel) {
          authChannel?.postMessage('login');
        }
        onClose && onClose();

        // Check for redirect URL stored in sessionStorage
        const redirectUrl = typeof window !== 'undefined'
          ? sessionStorage.getItem('redirectAfterLogin')
          : null;

        if (redirectUrl) {
          sessionStorage.removeItem('redirectAfterLogin');
          replace(redirectUrl);
        } else {
          replace('/my-page');
        }
      },
      ...restOptions,
    });
  };

  return {
    handleLogin,
    isPending,
  };
};

export default useLogin;
