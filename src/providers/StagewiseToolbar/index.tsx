/* eslint-disable no-console */
'use client';

import { useEffect } from 'react';

// Define your toolbar configuration with debugging
const stagewiseConfig = {
  plugins: [],
  port: 5746,
  // Add debugging options
  debug: true,
  // Prevent multiple connections
  autoConnect: true,
  reconnect: true,
};

export default function StageWiseToolbar() {
  useEffect(() => {
    // Only initialize in development mode and on client side
    if (process.env.NODE_ENV === 'development') {
      console.log('🎸 Initializing Stagewise toolbar...', stagewiseConfig);

      // Dynamic import to avoid SSR issues
      import('@stagewise/toolbar').then(({ initToolbar }) => {
        console.log('🎸 Stagewise package loaded, initializing...');

        try {
          const result = initToolbar(stagewiseConfig);
          console.log('🎸 Stagewise toolbar initialized successfully:', result);
        } catch (error) {
          console.error('🎸 Error during Stagewise initialization:', error);
        }
      }).catch((error) => {
        console.error('🎸 Failed to load Stagewise toolbar package:', error);
      });
    } else {
      console.log('🎸 Stagewise toolbar disabled (not in development mode)');
    }
  }, []); // Empty dependency array to prevent re-initialization

  // This component doesn't render anything visible
  return null;
}
