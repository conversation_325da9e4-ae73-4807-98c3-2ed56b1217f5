{"name": "webapp-common-flow-auth", "version": "0.1.0", "author": "", "license": "ISC", "scripts": {"dev": "npx @c2c-platform/mfe prepare && next dev", "dev:win": "next dev", "build": "next build && rimraf .next/cache .next/analyze .next/trace", "start": "next start", "build-stats": "cross-env ANALYZE=true npm run build", "export": "next export", "build-prod": "run-s clean build export", "clean": "rimraf .next out", "lint": "eslint .", "lint:fix": "eslint --fix .", "check-types": "tsc --noEmit --pretty", "prepare": "husky install", "storybook": "storybook dev -p 6006", "build-storybook": "storybook build"}, "dependencies": {"@c2c-platform/mfe": "^1.0.2", "@emotion/css": "^11.13.4", "@emotion/react": "^11.13.3", "@emotion/server": "^11.11.0", "@hookform/resolvers": "^3.9.1", "@mantine/carousel": "^7.14.0", "@mantine/core": "^7.14.0", "@mantine/dates": "^7.14.0", "@mantine/hooks": "^7.14.0", "@mantine/modals": "^7.14.0", "@mantine/next": "^6.0.22", "@mantine/notifications": "^7.14.0", "@mantine/nprogress": "^7.14.0", "@next/eslint-plugin-next": "^15.1.3", "@svgr/webpack": "^8.1.0", "@tanstack/react-query": "^5.59.20", "@tanstack/react-query-devtools": "^5.59.20", "@total-typescript/ts-reset": "^0.6.1", "@types/lodash": "^4.17.13", "@types/node": "^22", "@types/react": "^18", "@types/react-dom": "^18", "axios": "^1.7.7", "cookies-next": "^5.0.2", "country-flag-icons": "^1.5.19", "dayjs": "^1.11.13", "embla-carousel-react": "^8.3.1", "encoding": "^0.1.13", "fast-querystring": "^1.1.2", "linkify-react": "^4.1.3", "linkifyjs": "^4.1.3", "lodash": "^4.17.21", "next": "^14.2.3", "react": "^18", "react-device-detect": "^2.2.3", "react-dom": "^18", "react-hook-form": "^7.53.2", "react-image-crop": "^11.0.7", "react-number-format": "^5.4.2", "sass": "^1.80.7", "sharp": "^0.32.6", "typescript": "^5.6.3", "yup": "^1.4.0", "zustand": "^5.0.1"}, "devDependencies": {"@antfu/eslint-config": "^3.12.1", "@commitlint/cli": "^19.5.0", "@commitlint/config-conventional": "^19.5.0", "@eslint-react/eslint-plugin": "^1.23.0", "@next/bundle-analyzer": "^15.4.0-canary.51", "@stagewise-plugins/react": "^0.4.9", "@stagewise/toolbar-next": "^0.4.9", "@svgr/cli": "^8.1.0", "@typescript-eslint/eslint-plugin": "^7.11.0", "@typescript-eslint/parser": "^7.11.0", "autoprefixer": "^10.4.20", "cross-env": "^7.0.3", "eslint": "^9.17.0", "eslint-config-airbnb": "^19.0.4", "eslint-config-airbnb-typescript": "^18.0.0", "eslint-config-next": "14.2.3", "eslint-config-prettier": "^9.1.0", "eslint-plugin-format": "^0.1.3", "eslint-plugin-import": "^2.31.0", "eslint-plugin-jsx-a11y": "^6.10.2", "eslint-plugin-prettier": "^5.2.1", "eslint-plugin-react": "^7.37.2", "eslint-plugin-react-hooks": "^5.0.0", "eslint-plugin-react-refresh": "^0.4.16", "eslint-plugin-simple-import-sort": "^12.1.1", "eslint-plugin-tailwindcss": "^3.17.5", "eslint-plugin-unused-imports": "^4.1.4", "husky": "^9.1.6", "lint-staged": "^15.2.10", "postcss": "^8.4.49", "prettier": "^3.3.3", "rimraf": "^6.0.1", "tailwindcss": "^3.4.15"}}